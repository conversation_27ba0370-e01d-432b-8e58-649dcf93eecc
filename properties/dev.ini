# Central Environment Configuration File
# This file contains all the environment variables for all services
# Values from this file will be used to replace tokens in service .env.template files

[database]
# Kontratar Database Configuration
kontratar_db_host = govcon-1.cwjbmcfdk9qo.us-east-1.rds.amazonaws.com
kontratar_db_port = 5432
kontratar_db_name = postgres
kontratar_db_user = alpha
kontratar_db_password = 5t3r2i66123

# Customer Database Configuration  
customer_db_host = govcon-1.cwjbmcfdk9qo.us-east-1.rds.amazonaws.com
customer_db_port = 5432
customer_db_name = postgres
customer_db_user = alpha
customer_db_password = 5t3r2i66123

[application]
# Application Configuration
app_host = 0.0.0.0
app_port = 3011
debug = false 

[qdrant]
# Qdrant Vector Database Configuration
qdrant_host = localhost
qdrant_port = 6333
qdrant_grpc_port = 6334
qdrant_api_key = 
qdrant_timeout = 60
qdrant_prefer_grpc = true

[kontratar_legacy]
# Legacy Kontratar DB fields (duplicates for compatibility)
# db_kontratar_host = govcon-1.cwjbmcfdk9qo.us-east-1.rds.amazonaws.com
# db_kontratar_name = postgres
# db_kontratar_password = 5t3r2i66123
# db_kontratar_port = 5432
# db_kontratar_user = alpha


[langchain]
# LangChain Configuration
langchain_api_key = ***************************************************
langchain_project = ProposalGeneration
langchain_tracing_v2 = true

[scheduler]
# Scheduler Configuration
scheduler_interval_seconds = 60
scheduler_enable_on_startup = true
gemini_api_key=AIzaSyBnUx93PEPDAUi23KsA4ovcujXDWkRXRDA
openai_api_key=
llm_provider=ollama
llm_model=gemma3:27b
llm_url=http://ai.kontratar.com:11434
google_client_id=621216514510-n4pp2h3j7qknbo9eps6ma0a3hgjg043t.apps.googleusercontent.com
google_client_secret=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


[gsa_service]
# GSA Service specific configuration
gsa_scheduler_interval = 3600
gsa_scraper_timeout = 300
gsa_log_level = INFO

[forecast_service]
# Forecast Data Feed Service configuration
forecast_update_interval = 1800
forecast_log_level = INFO

[state_opportunity_service]
# State Opportunity Service configuration
state_scraper_interval = 7200
state_log_level = INFO
