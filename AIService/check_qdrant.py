#!/usr/bin/env python3

import asyncio
import sys
from pathlib import Path

sys.path.append('.')

try:
    from dotenv import load_dotenv
    load_dotenv('.env')
except ImportError:
    print("Warning: python-dotenv not available")

import httpx
from qdrant_client import QdrantClient
from config import settings

async def check_qdrant_connectivity():
    """Check if Qdrant instance is accessible and find relevant collections"""

    qdrant_url = settings.qdrant_url

    opportunity_id = "vSe1unlCj9"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"

    print("Qdrant Connectivity Check")
    print("=" * 50)
    print(f"Looking for collections related to:")
    print(f"  Opportunity ID: {opportunity_id}")
    print(f"  Tenant ID: {tenant_id}")
    print(f"  Expected pattern: {tenant_id}_{opportunity_id}")
    print()

    accessible_instance = False
    matching_collections = []

    print(f"1. Checking {qdrant_url}...")

    try:
        # Test basic connectivity using Qdrant client
        client = QdrantClient(url=qdrant_url)

        # Test by getting collections (this will verify connectivity)
        collections = client.get_collections()
        print(f"   ✓ Qdrant is accessible")
        print(f"   ✓ Found {len(collections.collections)} collections")
        accessible_instance = True

        # List collections (already retrieved above)
        collection_names = [col.name for col in collections.collections]

        matches = []
        for name in collection_names:
            if (opportunity_id in name or
                tenant_id in name or
                name.startswith(f"{tenant_id}_{opportunity_id}") or
                name.startswith(opportunity_id)):
                matches.append(name)

        if matches:
            print(f"   ✓ Matching collections: {matches}")
            matching_collections.extend([(qdrant_url, match) for match in matches])
        else:
            print(f"   ⚠ No matching collections")
            # Show sample collection names
            if collection_names:
                sample = collection_names[:3]
                print(f"   Sample collections: {sample}")

    except Exception as e:
        print(f"   ❌ Connection failed: {e}")

    print()
    
    # Summary
    print("=" * 50)
    print("SUMMARY")
    print("=" * 50)

    if accessible_instance:
        print(f"✓ Qdrant instance accessible: {qdrant_url}")
    else:
        print(f"❌ Qdrant instance not accessible: {qdrant_url}")

    print()

    if matching_collections:
        print(f"✓ Found {len(matching_collections)} matching collections:")
        for qdrant_url, collection_name in matching_collections:
            print(f"  {collection_name}")
            print(f"     at {qdrant_url}")
        print()
        print("🎉 Qdrant connectivity is working and collections exist!")
        return True
    else:
        print("⚠ No matching collections found for the test opportunity")
        print()
        if accessible_instance:
            print("Qdrant is accessible but no data exists for this opportunity.")
            print("   You may need to:")
            print("   1. Upload/ingest documents for this opportunity")
            print("   2. Check if the opportunity ID and tenant ID are correct")
            print("   3. Verify the collection naming convention")
        else:
            print("Qdrant instance is not accessible.")
            print("   Please check:")
            print("   1. Network connectivity")
            print("   2. Qdrant service status")
            print("   3. Firewall settings")
        return False

async def check_database_records():
    """Check database for vector database instance records"""

    print("\n" + "=" * 50)
    print("DATABASE RECORDS CHECK")
    print("=" * 50)

    try:
        from database import get_kontratar_db
        from models.kontratar_models import ChromaDBInstanceMapping
        from sqlalchemy import select

        opportunity_id = "vSe1unlCj9"
        tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"

        async for db in get_kontratar_db():
            query = select(ChromaDBInstanceMapping).where(
                ChromaDBInstanceMapping.unique_id == opportunity_id,
                ChromaDBInstanceMapping.tenant_id == tenant_id
            )
            result = await db.execute(query)
            vector_instances = result.scalars().all()

            if vector_instances:
                print(f"✓ Found {len(vector_instances)} database records:")
                for i, instance in enumerate(vector_instances, 1):
                    print(f"  {i}. Collection: {instance.collection_name}")
                    print(f"     URL: {instance.chroma_instance_url}")
                    print(f"     Version: {instance.version}")
                    print(f"     Status: {instance.status}")
                return True
            else:
                print("⚠ No database records found for this opportunity")
                return False
            break

    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False

def main():
    """Main function"""

    try:
        qdrant_ok = asyncio.run(check_qdrant_connectivity())

        db_ok = asyncio.run(check_database_records())

        print("\n" + "=" * 50)
        print("FINAL RESULT")
        print("=" * 50)

        if qdrant_ok and db_ok:
            print("✅ All checks passed! Qdrant is ready for testing.")
            sys.exit(0)
        elif qdrant_ok:
            print("⚠ Qdrant is accessible but no database records found.")
            print("   The compliance tests may not work without proper data setup.")
            sys.exit(1)
        else:
            print("❌ Qdrant connectivity issues detected.")
            print("   Please resolve connectivity before running compliance tests.")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\nInterrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
