#!/usr/bin/env python3
"""
Test script for Chain of Thought implementation in proposal generation.

This script demonstrates how the chain of thought reasoning enhances
the proposal generation process with step-by-step analysis.
"""

import asyncio
import json
from datetime import datetime
from services.proposal.chain_of_thought_service import ChainOfThoughtService, ChainOfThoughtIntegration
from services.proposal.outline import ProposalOutlineService
from loguru import logger


async def test_chain_of_thought_basic():
    """Test basic chain of thought functionality."""
    
    print("🧠 Testing Chain of Thought Service")
    print("=" * 60)
    
    # Initialize the service
    cot_service = ChainOfThoughtService()
    
    # Test data
    section_title = "Technical Approach"
    section_description = """
    Describe your technical approach to implementing the cloud-based data analytics platform.
    Include methodology, architecture, security measures, and implementation timeline.
    Address scalability, performance, and compliance with federal data protection standards.
    """
    
    rfp_context = """
    The government requires a cloud-based analytics platform capable of processing
    large datasets with real-time visualization capabilities. The solution must
    comply with FedRAMP standards and support multi-tenant architecture.
    Performance requirements include sub-second query response times for datasets
    up to 10TB. The system must integrate with existing government databases
    and provide role-based access controls.
    """
    
    client_context = """
    Our company specializes in cloud analytics solutions with 15+ years of experience.
    We have successfully implemented similar platforms for 3 federal agencies.
    Our team includes certified cloud architects and data scientists.
    We maintain FedRAMP authorization and have extensive experience with government compliance.
    """
    
    compliance_requirements = {
        "page_limit": 5,
        "required_elements": ["technical architecture", "security measures", "implementation timeline"],
        "prohibited_elements": ["pricing information", "marketing content"],
        "evaluation_criteria": ["technical feasibility", "innovation", "compliance"]
    }
    
    try:
        print(f"📋 Section: {section_title}")
        print(f"📄 Page Limit: {compliance_requirements['page_limit']} pages")
        print(f"🔍 Testing chain of thought reasoning...")
        
        # Generate content with chain of thought
        result = await cot_service.generate_section_with_chain_of_thought(
            section_title=section_title,
            section_description=section_description,
            section_number="3.1",
            page_limit=compliance_requirements["page_limit"],
            rfp_context=rfp_context,
            client_context=client_context,
            tenant_metadata="Professional cloud analytics company",
            compliance_requirements=compliance_requirements,
            previous_sections_summary="Previous sections covered project understanding and management approach.",
            is_cover_letter=False,
            is_personnel_section=False,
            personnel_context=""
        )
        
        if result.get('success', False):
            print("✅ Chain of thought generation successful!")
            print(f"📏 Content length: {len(result['content'])} characters")
            print(f"📊 Word count: {result['metadata']['word_count']} words")
            print(f"🎯 Reasoning quality: {result['metadata']['reasoning_quality']}")
            print(f"✔️ Validation passed: {result['metadata']['validation_passed']}")
            
            print("\n🧠 Reasoning Steps Applied:")
            for i, step in enumerate(result['reasoning_steps'], 1):
                print(f"   {i}. {step}")
            
            print(f"\n📝 Generated Content Preview:")
            print("-" * 40)
            print(result['content'][:500] + "..." if len(result['content']) > 500 else result['content'])
            print("-" * 40)
            
            # Save detailed results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"chain_of_thought_test_{timestamp}.json"
            
            with open(filename, 'w') as f:
                json.dump(result, f, indent=2, default=str)
            
            print(f"💾 Detailed results saved to: {filename}")
            
        else:
            print("❌ Chain of thought generation failed!")
            print(f"Error: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        logger.error(f"Chain of thought test failed: {e}")


async def test_integration_with_proposal_service():
    """Test integration with the existing ProposalOutlineService."""
    
    print("\n🔗 Testing Integration with ProposalOutlineService")
    print("=" * 60)
    
    try:
        # Initialize the proposal service (which should now include chain of thought)
        proposal_service = ProposalOutlineService()
        
        if hasattr(proposal_service, 'chain_of_thought_enabled') and proposal_service.chain_of_thought_enabled:
            print("✅ Chain of thought integration detected in ProposalOutlineService")
            print("🧠 Enhanced reasoning capabilities are available")
            
            # Test the integration
            cot_integration = proposal_service.cot_integration
            
            test_result = await cot_integration.enhance_draft_generation_with_cot(
                section_title="Management Approach",
                section_description="Describe your project management methodology and team structure.",
                section_number="2.1",
                page_limit=3,
                rfp_context="Government project requires agile methodology with weekly reporting.",
                client_context="Our team uses certified PMP managers and agile practices.",
                tenant_metadata="Professional services company",
                compliance_requirements={"page_limit": 3, "required_elements": ["methodology", "team structure"]},
                previous_sections_summary="",
                is_cover_letter=False,
                is_personnel_section=False,
                personnel_context=""
            )
            
            if test_result.get('success', False):
                print("✅ Integration test successful!")
                print(f"🧠 Enhanced with chain of thought: {test_result.get('enhanced_with_cot', False)}")
                print(f"📏 Content length: {len(test_result.get('content', ''))}")
            else:
                print("⚠️ Integration test completed with fallback")
                print(f"Reason: {test_result.get('reason', 'Unknown')}")
                
        else:
            print("⚠️ Chain of thought not enabled in ProposalOutlineService")
            print("This could be due to:")
            print("  - Import issues with the chain of thought service")
            print("  - Initialization problems")
            print("  - Service not properly integrated")
            
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        logger.error(f"Integration test failed: {e}")


async def test_section_complexity_detection():
    """Test the LLM-based logic that determines when to use chain of thought."""

    print("\n🎯 Testing LLM-Based Section Complexity Detection")
    print("=" * 60)

    try:
        cot_service = ChainOfThoughtService()

        test_cases = [
            {
                "title": "Cover Letter",
                "description": "Brief introduction letter for the proposal submission",
                "page_limit": 1,
                "compliance": {},
                "expected_range": (0, 40)  # Should be low complexity
            },
            {
                "title": "Technical Approach and Solution Architecture",
                "description": "Comprehensive technical solution including system architecture, security framework, data integration methodology, scalability design, performance optimization strategies, and implementation roadmap with risk mitigation",
                "page_limit": 8,
                "compliance": {"evaluation_criteria": ["technical", "innovation", "feasibility"], "required_elements": ["architecture", "security", "performance"]},
                "expected_range": (70, 100)  # Should be high complexity
            },
            {
                "title": "Past Performance",
                "description": "Demonstrate relevant experience with similar government projects including metrics, outcomes, and client references",
                "page_limit": 4,
                "compliance": {"required_elements": ["project examples", "metrics", "references"]},
                "expected_range": (60, 85)  # Should be moderate to high complexity
            },
            {
                "title": "Company Information",
                "description": "Basic company overview and contact information",
                "page_limit": 2,
                "compliance": {},
                "expected_range": (0, 35)  # Should be low complexity
            },
            {
                "title": "Quality Assurance and Risk Management",
                "description": "Detailed quality assurance methodology, risk identification and mitigation strategies, contingency planning, and performance monitoring framework",
                "page_limit": 5,
                "compliance": {"evaluation_criteria": ["quality", "risk management"], "required_elements": ["QA process", "risk matrix"]},
                "expected_range": (65, 90)  # Should be high complexity
            }
        ]

        print("Testing LLM-based complexity assessment:")
        print("(This may take a moment as it uses LLM analysis...)")

        for i, case in enumerate(test_cases, 1):
            print(f"\n  {i}. Analyzing: {case['title']}")

            try:
                # Test the LLM-based complexity assessment
                complexity_score = await cot_service._assess_section_complexity_with_llm(
                    case["title"],
                    case["description"],
                    case["page_limit"],
                    case["compliance"]
                )

                # Test the overall decision
                should_use_cot = await cot_service._should_use_chain_of_thought(
                    case["title"],
                    case["description"],
                    case["page_limit"],
                    case["compliance"]
                )

                min_expected, max_expected = case["expected_range"]
                score_in_range = min_expected <= complexity_score <= max_expected

                status = "✅" if score_in_range else "⚠️"
                cot_decision = "COT" if should_use_cot else "Standard"

                print(f"     {status} Complexity Score: {complexity_score}/100 (expected: {min_expected}-{max_expected})")
                print(f"     🧠 Decision: {cot_decision}")

                if not score_in_range:
                    print(f"     ⚠️ Score outside expected range - LLM assessment may differ from expectations")

            except Exception as e:
                print(f"     ❌ Assessment failed: {e}")

        print(f"\n🧠 LLM-Based Complexity Assessment Benefits:")
        print(f"   ✅ Intelligent analysis beyond keyword matching")
        print(f"   ✅ Considers strategic importance and evaluation impact")
        print(f"   ✅ Adapts to context and requirements dynamically")
        print(f"   ✅ Provides nuanced complexity scoring (0-100)")

    except Exception as e:
        print(f"❌ Complexity detection test failed: {e}")


async def test_complexity_assessment_performance():
    """Test the performance and reliability of LLM-based complexity assessment."""

    print("\n⚡ Testing Complexity Assessment Performance")
    print("=" * 60)

    try:
        cot_service = ChainOfThoughtService()

        # Test with a moderately complex section
        test_title = "Management Approach and Team Structure"
        test_description = "Describe project management methodology, team organization, communication protocols, and quality control processes"

        print(f"Testing performance with: {test_title}")

        import time
        start_time = time.time()

        complexity_score = await cot_service._assess_section_complexity_with_llm(
            test_title, test_description, 3, {"required_elements": ["methodology", "team"]}
        )

        end_time = time.time()
        assessment_time = end_time - start_time

        print(f"✅ Assessment completed in {assessment_time:.2f} seconds")
        print(f"📊 Complexity Score: {complexity_score}/100")

        if assessment_time < 10:
            print("✅ Performance: Excellent (< 10 seconds)")
        elif assessment_time < 20:
            print("⚠️ Performance: Acceptable (10-20 seconds)")
        else:
            print("❌ Performance: Slow (> 20 seconds)")

        # Test fallback behavior
        print(f"\n🔄 Testing fallback behavior...")
        fallback_result = cot_service._fallback_complexity_assessment(
            test_title, test_description, 3, {"required_elements": ["methodology", "team"]}
        )

        print(f"✅ Fallback assessment: {'COT' if fallback_result else 'Standard'}")

    except Exception as e:
        print(f"❌ Performance test failed: {e}")


async def main():
    """Run all chain of thought tests."""
    
    print("🧪 Chain of Thought Testing Suite")
    print("=" * 80)
    print("Testing the implementation of step-by-step reasoning in proposal generation")
    print("=" * 80)
    
    try:
        # Test 1: Basic chain of thought functionality
        await test_chain_of_thought_basic()
        
        # Test 2: Integration with proposal service
        await test_integration_with_proposal_service()
        
        # Test 3: LLM-based section complexity detection
        await test_section_complexity_detection()

        # Test 4: Performance and reliability
        await test_complexity_assessment_performance()

        print("\n🎉 All tests completed!")
        print("=" * 60)
        print("Key Benefits of Enhanced Chain of Thought Implementation:")
        print("✅ Step-by-step reasoning for complex sections")
        print("✅ Enhanced compliance analysis and verification")
        print("✅ Strategic content planning and optimization")
        print("✅ Improved quality and consistency")
        print("✅ Seamless integration with existing system")
        print("✅ Intelligent LLM-based complexity assessment")
        print("✅ Dynamic adaptation to section requirements")
        print("✅ Graceful fallback to standard generation")
        
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        logger.error(f"Test suite failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
