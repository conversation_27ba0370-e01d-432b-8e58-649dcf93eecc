from datetime import datetime
from typing import List
from loguru import logger
from models.customer_models import ProposalOutlineQueue, NotificationQueue, Users
from models.kontratar_models import OppsTable
from sqlalchemy import select, update, insert
from sqlalchemy.ext.asyncio import AsyncSession
from models.kontratar_models import SamOpportunityQueue
from models.customer_models import CustomOppsQueue as CustomerCustomOppsQueue
from database import get_customer_db
import json

class ProposalOutlineQueueController:
    
    @staticmethod
    async def claim_new_queue_items(db: AsyncSession, limit: int = 6) -> List[ProposalOutlineQueue]:
        """
        Atomically claim up to `limit` NEW items and mark them as CLAIMED,
        but only if their TOC fields are present in the opportunity tables.
        For 'sam': oppstable.toc_text to toc_text5 not all empty.
        For 'custom': custom_oppstable.toc_text to toc_text_5 not all empty.
        """
        try:
            # Select NEW items
            cte = (
                select(ProposalOutlineQueue)
                .where(ProposalOutlineQueue.status == "NEW")
                .order_by(ProposalOutlineQueue.created_date.desc())
                .limit(limit * 4)  # Over-select to allow for filtering
                .with_for_update(skip_locked=True)
            ).cte("to_claim")
    
            # Fetch candidate items
            candidate_query = select(ProposalOutlineQueue).where(
                ProposalOutlineQueue.id.in_(select(cte.c.id))
            )
            result = await db.execute(candidate_query)
            candidates = result.scalars().all()
    
            # Separate opps_ids by outline_type
            sam_opps_ids = [item.opps_id for item in candidates if (item.outline_type or "").lower() == "sam"]
            custom_opps_ids = [item.opps_id for item in candidates if (item.outline_type or "").lower() == "custom"]
    
            # Batch fetch TOC fields for SAM
            sam_tocs = set()
            if sam_opps_ids:
                toc_query = select(
                    OppsTable.notice_id,
                    OppsTable.toc_text, OppsTable.toc_text2, OppsTable.toc_text3, OppsTable.toc_text4, OppsTable.toc_text5
                ).where(OppsTable.notice_id.in_(sam_opps_ids))
                toc_result = await db.execute(toc_query)
                for row in toc_result.fetchall():
                    if any(row[1:]):  # Any TOC field is not empty
                        sam_tocs.add(row[0])
    
            # Batch fetch TOC fields for CUSTOM
            custom_tocs = set()
            custom_tenant_map = {}
            if custom_opps_ids:
                from models.customer_models import CustomOppsTable
                toc_query = select(
                    CustomOppsTable.opportunity_id,
                    CustomOppsTable.tenant_id,  # gets tenant id for custom opps
                    CustomOppsTable.toc_text, CustomOppsTable.toc_text_2, CustomOppsTable.toc_text_3,
                    CustomOppsTable.toc_text_4, CustomOppsTable.toc_text_5
                ).where(CustomOppsTable.opportunity_id.in_(custom_opps_ids))
                toc_result = await db.execute(toc_query)
                for row in toc_result.fetchall():
                    opp_id = row[0]
                    tenant_id = row[1]
                    if any(row[2:]):
                        custom_tocs.add(opp_id)
                    custom_tenant_map[opp_id] = tenant_id

    
            # Filter valid items
            valid_ids = []
            for item in candidates:
                if (item.outline_type or "").lower() == "sam":
                    if item.opps_id in sam_tocs:
                        valid_ids.append(item.id)
                    else:
                        # All TOC fields are empty for this SAM item
                        # Set status to WAITING_FOR_TOC before ensuring source queue entry
                        item.status = "WAITING_FOR_TOC"
                        item.last_updated_date = datetime.utcnow()
                        db.add(item)
                        await db.commit()
                        await ProposalOutlineQueueController.ensure_source_queue_entry_for_empty_toc(
                             item.opps_id, "sam"
                        )
                    
                elif (item.outline_type or "").lower() == "custom":
                    if item.opps_id in custom_tocs:
                        valid_ids.append(item.id)
                    else:
                        
                        # All TOC fields are empty for this CUSTOM item
                        # Set status to WAITING_FOR_TOC before ensuring source queue entry
                        item.status = "WAITING_FOR_TOC"
                        item.last_updated_date = datetime.utcnow()
                        db.add(item)
                        await db.commit()
                        tenant_id = custom_tenant_map.get(item.opps_id, "SYSTEM")
                        await ProposalOutlineQueueController.ensure_source_queue_entry_for_empty_toc(
                             item.opps_id, "custom", tenant_id=tenant_id
                        )
    
            # Limit to requested batch size
            valid_ids = valid_ids[:limit]
    
            if not valid_ids:
                return []
    
            # Claim only valid items
            update_stmt = (
                update(ProposalOutlineQueue)
                .where(ProposalOutlineQueue.id.in_(valid_ids))
                .values(status="CLAIMED", last_updated_date=datetime.utcnow())
                .returning(ProposalOutlineQueue)
            )
            result = await db.execute(update_stmt)
            await db.commit()
    
            claimed_items = result.scalars().all()
            logger.info(f"Claimed {len(claimed_items)} outline items for processing")
            return list(claimed_items)
        except Exception as e:
            logger.error(f"Error claiming Proposal Outline queue items: {e}")
            await db.rollback()
            return []
        
    @staticmethod
    async def ensure_source_queue_entry_for_empty_toc(
        opps_id: str,
        outline_type: str,
        tenant_id: str = None
    ) -> None:
        """
        If all TOC fields are empty for the given opps_id:
        - For 'sam': ensure SamOpportunityQueue exists, set status to 'NEW'
        - For 'custom': ensure CustomOppsQueue exists, set status to 'COMPLETED'
        Only acts on the source queue, does nothing in ProposalOutlineQueue.
        """
        try:
            async for db in get_customer_db():
                if outline_type.lower() == "sam":
                    # Check SamOpportunityQueue for this opps_id
                    query = select(SamOpportunityQueue).where(SamOpportunityQueue.opps_id == opps_id)
                    result = await db.execute(query)
                    entry = result.scalars().first()
                    if entry:
                        entry.status = "NEW"
                        entry.updated_date = datetime.utcnow()
                        entry.created_date = datetime.utcnow()
                        db.add(entry)
                        logger.info(f"Updated SamOpportunityQueue entry for opps_id={opps_id} to status NEW")
                    else:
                        entry = SamOpportunityQueue(
                            opps_id=opps_id,
                            status="NEW",
                            created_date=datetime.utcnow(),
                            updated_date=datetime.utcnow()
                        )
                        db.add(entry)
                        logger.info(f"Created SamOpportunityQueue entry for opps_id={opps_id} with status NEW")
                elif outline_type.lower() == "custom":
                    # Check CustomOppsQueue for this opps_id
                    query = select(CustomerCustomOppsQueue).where(CustomerCustomOppsQueue.opps_id == opps_id)
                    result = await db.execute(query)
                    entry = result.scalars().first()
                    if entry:
                        entry.status = "COMPLETED"
                        entry.update_date = datetime.utcnow()
                        entry.created_date = datetime.utcnow()
                        db.add(entry)
                        logger.info(f"Updated CustomOppsQueue entry for opps_id={opps_id} to status COMPLETED")
                    else:
                        entry = CustomerCustomOppsQueue(
                            opps_id=opps_id,
                            opps_source="CUSTOM",
                            tenant_id=tenant_id,
                            status="COMPLETED",
                            created_date=datetime.utcnow(),
                            update_date=datetime.utcnow()
                        )
                        db.add(entry)
                        logger.info(f"Created CustomOppsQueue entry for opps_id={opps_id} with status COMPLETED")
                else:
                    logger.warning(f"Unknown outline_type '{outline_type}' for opps_id={opps_id}")
                    return
        
                await db.commit()
                if entry:
                    await db.refresh(entry)
                break
        except Exception as e:
            logger.error(f"Error ensuring source queue entry for opps_id={opps_id}, outline_type={outline_type}: {e}")
                
    
    @staticmethod
    async def complete_and_notify(db: AsyncSession, item_id: int, outline_type: str):
        # 1. Mark the completed item as COMPLETED
        update_stmt = (
            update(ProposalOutlineQueue)
            .where(ProposalOutlineQueue.id == item_id)
            .values(status="COMPLETED", last_updated_date=datetime.utcnow())
        )
        await db.execute(update_stmt)
        await db.commit()
        
        

        # 2. Get the item to check first_request and opps_id
        item_query = select(ProposalOutlineQueue).where(ProposalOutlineQueue.id == item_id)
        item_result = await db.execute(item_query)
        item = item_result.scalar_one_or_none()
        if not item or not item.first_request:
            return  # Only proceed if first_request is True

        opps_id = item.opps_id

        # 3. Mark all queue items with this opps_id as COMPLETED
        update_all_stmt = (
            update(ProposalOutlineQueue)
            .where(ProposalOutlineQueue.opps_id == opps_id)
            .values(status="COMPLETED", last_updated_date=datetime.utcnow())
        )
        await db.execute(update_all_stmt)
        await db.commit()

        # 3b. Update the ProposalQueue items to reflect the outline status
        from models.customer_models import ProposalQueue
        pq_query = select(ProposalQueue).where(
            ProposalQueue.opps_id == opps_id,
            ProposalQueue.status == "WAITING_FOR_OUTLINE"
        )
        pq_result = await db.execute(pq_query)
        pq_items = pq_result.scalars().all()
        for pq_item in pq_items:
            pq_item.status = "N"
            pq_item.next_state = "OUTLINE_READY"
            db.add(pq_item)
        if pq_items:
            await db.commit()
            logger.info(f"Updated {len(pq_items)} ProposalQueue items for opps_id={opps_id} to status 'N' and next_state 'OUTLINE_READY'")
        

        # 4. Get all tenant_ids for these items
        tenants_query = select(ProposalOutlineQueue.tenant_id).where(
        ProposalOutlineQueue.opps_id == opps_id,
        ProposalOutlineQueue.tenant_id != "SYSTEM"
        )
        tenants_result = await db.execute(tenants_query)
        tenant_ids = set(row[0] for row in tenants_result.fetchall())

           # 5. Get the opportunity title dynamically
        opp_title = opps_id  # Default if not found
        
        if (outline_type or "").lower() == "sam":
            opp_query = select(OppsTable.title).where(OppsTable.notice_id == opps_id)
            opp_result = await db.execute(opp_query)
            opp_title_row = opp_result.first()
            if opp_title_row:
                opp_title = opp_title_row[0]
        elif (outline_type or "").lower() == "custom":
            from models.customer_models import CustomOppsTable
            opp_query = select(CustomOppsTable.title).where(CustomOppsTable.opportunity_id == opps_id)
            opp_result = await db.execute(opp_query)
            opp_title_row = opp_result.first()
            if opp_title_row:
                opp_title = opp_title_row[0]

        # 6. For each tenant, get admin users (group_id 0 or 1)
        for tenant_id in tenant_ids:
            users_query = select(Users.id).where(
                Users.tenant_id == tenant_id,
                Users.group_id.in_([0, 1])
            )
            users_result = await db.execute(users_query)
            user_ids = [row[0] for row in users_result.fetchall()]

            # 7. Create notification for each admin user
            for user_id in user_ids:
                notif = NotificationQueue(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    job_info=json.dumps({"link": f"/sam/{opps_id}"}),
                    status="NEW",
                    title="Proposal Outline Ready",
                    message=f"Outline generation completed successfully for {opp_title}",
                    created_at=datetime.utcnow()
                )
                db.add(notif)
        await db.commit()
        
        
    @staticmethod
    async def update_queue_status(db: AsyncSession, item_id: int, status: str, error_message: str = None):
        """
        Update the status (and optionally error_message) of a ProposalOutlineQueue item.
        """
        try:
            update_fields = {
                "status": status,
                "last_updated_date": datetime.utcnow()
            }
            if error_message is not None:
                update_fields["error_message"] = error_message

            update_stmt = (
                update(ProposalOutlineQueue)
                .where(ProposalOutlineQueue.id == item_id)
                .values(**update_fields)
            )
            await db.execute(update_stmt)
            await db.commit()
            logger.info(f"Updated ProposalOutlineQueue item {item_id} to status '{status}'")
        except Exception as e:
            logger.error(f"Error updating ProposalOutlineQueue item {item_id} status: {e}")
            await db.rollback()
        
        
    @staticmethod
    async def add_to_queue_from_worker(
        db: AsyncSession,
        opps_id: str,
        tenant_id: str,
        outline_type: str = "sam",
        first_request: bool = True,
        status: str = "NEW",
        error_message: str = None
    ) -> ProposalOutlineQueue:
        """
        Add a new entry to the ProposalOutlineQueue if not already present.
        If an entry with opps_id and first_request=True exists:
          - If status is NEW, update created_date.
          - If status is CLAIMED or PROCESSING, do nothing.
          - Otherwise, add a new entry.
        """
        try:
            # Check for existing entry
            query = select(ProposalOutlineQueue).where(
                ProposalOutlineQueue.opps_id == opps_id,
                ProposalOutlineQueue.first_request == True
            )
            result = await db.execute(query)
            existing = result.scalar_one_or_none()
    
            if existing:
                if existing.status == "NEW":
                    # Update created_date to now
                    existing.created_date = datetime.utcnow()
                    db.add(existing)
                    await db.commit()
                    await db.refresh(existing)
                    logger.info(f"Updated created_date for existing ProposalOutlineQueue entry (NEW) opps_id={opps_id}, ")
                    return existing
                elif existing.status in ("CLAIMED", "PROCESSING"):
                    # Do nothing
                    logger.info(f"ProposalOutlineQueue entry for opps_id={opps_id} is already CLAIMED or PROCESSING. No action taken.")
                    return existing
                elif existing.status in ("FAILED", "WAITING_FOR_TOC"):
                    existing.status = "NEW"
                    existing.last_updated_date = datetime.utcnow()
                    existing.created_date = datetime.utcnow()
                    db.add(existing)
                    await db.commit()
                    await db.refresh(existing)
                    logger.info(f"Updated status to NEW for existing ProposalOutlineQueue entry ({existing.status}) opps_id={opps_id}")
                    return existing
                else:
                    # For other statuses, add a new entry
                    pass
    
            # If no entry exists, add new
            new_entry = ProposalOutlineQueue(
                opps_id=opps_id,
                tenant_id=tenant_id,
                outline_type=outline_type,
                first_request=first_request,
                status=status,
                error_message=error_message,
                created_date=datetime.utcnow(),
                last_updated_date=datetime.utcnow()
            )
            db.add(new_entry)
            await db.commit()
            await db.refresh(new_entry)
            logger.info(f"Added ProposalOutlineQueue entry for opps_id={opps_id}, tenant_id={tenant_id}, outline_type={outline_type}")
            return new_entry
    
        except Exception as e:
            logger.error(f"Error adding ProposalOutlineQueue entry: {e}")
            await db.rollback()
            return None