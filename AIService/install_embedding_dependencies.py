#!/usr/bin/env python3
"""
Install Embedding Dependencies
==============================

This script helps install the required dependencies for different embedding providers.

Usage:
    python install_embedding_dependencies.py [--provider all|openai|gemini|sentence-transformers]
"""

import argparse
import subprocess
import sys
from typing import List, Dict

def run_pip_install(packages: List[str]) -> bool:
    """Install packages using pip"""
    try:
        for package in packages:
            print(f"Installing {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Successfully installed {package}")
            else:
                print(f"❌ Failed to install {package}")
                print(f"Error: {result.stderr}")
                return False
        return True
    except Exception as e:
        print(f"❌ Error installing packages: {e}")
        return False

def install_base_dependencies():
    """Install base dependencies required for all providers"""
    print("📦 Installing base dependencies...")
    base_packages = [
        "fastapi",
        "uvicorn[standard]",
        "pydantic",
        "requests",
        "loguru"
    ]
    return run_pip_install(base_packages)

def install_openai_dependencies():
    """Install OpenAI dependencies"""
    print("🤖 Installing OpenAI dependencies...")
    openai_packages = [
        "openai>=1.0.0"
    ]
    return run_pip_install(openai_packages)

def install_gemini_dependencies():
    """Install Google Gemini dependencies"""
    print("💎 Installing Google Gemini dependencies...")
    gemini_packages = [
        "google-generativeai"
    ]
    return run_pip_install(gemini_packages)

def install_sentence_transformers_dependencies():
    """Install Sentence Transformers dependencies"""
    print("🧠 Installing Sentence Transformers dependencies...")
    st_packages = [
        "sentence-transformers",
        "torch",  # Required for sentence-transformers
        "numpy"
    ]
    return run_pip_install(st_packages)

def check_existing_installations():
    """Check which packages are already installed"""
    print("🔍 Checking existing installations...")
    
    packages_to_check = {
        "fastapi": "Base dependency",
        "uvicorn": "Base dependency", 
        "openai": "OpenAI provider",
        "google-generativeai": "Gemini provider",
        "sentence-transformers": "Sentence Transformers provider",
        "torch": "PyTorch (for Sentence Transformers)"
    }
    
    installed = {}
    for package, description in packages_to_check.items():
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "show", package
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                # Extract version from pip show output
                for line in result.stdout.split('\n'):
                    if line.startswith('Version:'):
                        version = line.split(':', 1)[1].strip()
                        installed[package] = version
                        print(f"✅ {package} ({description}): v{version}")
                        break
            else:
                print(f"❌ {package} ({description}): Not installed")
                installed[package] = None
        except Exception as e:
            print(f"⚠️  Error checking {package}: {e}")
            installed[package] = None
    
    return installed

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Install Embedding Dependencies",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Install all dependencies
  python install_embedding_dependencies.py --provider all

  # Install only OpenAI dependencies
  python install_embedding_dependencies.py --provider openai

  # Install only Sentence Transformers dependencies
  python install_embedding_dependencies.py --provider sentence-transformers

  # Check what's already installed
  python install_embedding_dependencies.py --check-only
        """
    )
    
    parser.add_argument("--provider", 
                       choices=["all", "openai", "gemini", "sentence-transformers"],
                       default="all",
                       help="Which provider dependencies to install")
    parser.add_argument("--check-only", action="store_true",
                       help="Only check existing installations, don't install anything")
    
    args = parser.parse_args()
    
    print("="*80)
    print("EMBEDDING DEPENDENCIES INSTALLER")
    print("="*80)
    
    # Check existing installations
    installed = check_existing_installations()
    
    if args.check_only:
        print("\n📋 Installation Summary:")
        print("-" * 40)
        
        base_deps = ["fastapi", "uvicorn"]
        openai_deps = ["openai"]
        gemini_deps = ["google-generativeai"]
        st_deps = ["sentence-transformers", "torch"]
        
        print(f"Base dependencies: {'✅' if all(installed.get(dep) for dep in base_deps) else '❌'}")
        print(f"OpenAI provider: {'✅' if all(installed.get(dep) for dep in openai_deps) else '❌'}")
        print(f"Gemini provider: {'✅' if all(installed.get(dep) for dep in gemini_deps) else '❌'}")
        print(f"Sentence Transformers: {'✅' if all(installed.get(dep) for dep in st_deps) else '❌'}")
        
        return
    
    print(f"\n🚀 Installing dependencies for: {args.provider}")
    print("-" * 40)
    
    success = True
    
    # Always install base dependencies
    if not install_base_dependencies():
        success = False
    
    # Install provider-specific dependencies
    if args.provider in ["all", "openai"]:
        if not install_openai_dependencies():
            success = False
    
    if args.provider in ["all", "gemini"]:
        if not install_gemini_dependencies():
            success = False
    
    if args.provider in ["all", "sentence-transformers"]:
        if not install_sentence_transformers_dependencies():
            success = False
    
    print("\n" + "="*80)
    if success:
        print("🎉 All dependencies installed successfully!")
        print("\nNext steps:")
        print("1. Configure your .env file with the desired provider")
        print("2. Start the embedding server: python start_embedding_server.py")
        print("3. Test the service: python test_embedding_service.py")
    else:
        print("💥 Some dependencies failed to install.")
        print("Please check the error messages above and try again.")
    
    print("="*80)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
