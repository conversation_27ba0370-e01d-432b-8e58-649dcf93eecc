#!/usr/bin/env python3
"""
Test script to investigate SAM data format and encryption
"""

import asyncio
import sys
import os
import base64
import re

# Add the parent directory to the path so we can import from AIService
sys.path.append('.')

from database import get_kontratar_db
from models.kontratar_models import OpportunityTableInfo
from sqlalchemy import select


async def investigate_sam_data():
    """Investigate the format of data in OpportunityTableInfo table"""
    print("🔍 Investigating SAM data format...")
    
    try:
        async for db in get_kontratar_db():
            # Get a few sample records
            query = select(
                OpportunityTableInfo.opps_id,
                OpportunityTableInfo.opps_raw_text,
                OpportunityTableInfo.status
            ).limit(5)
            
            result = await db.execute(query)
            rows = result.fetchall()
            
            print(f"Found {len(rows)} sample records:")
            print("="*80)
            
            for i, row in enumerate(rows, 1):
                print(f"\n[{i}] Opportunity ID: {row.opps_id}")
                print(f"    Status: {row.status}")
                
                raw_text = row.opps_raw_text
                if raw_text:
                    print(f"    Data type: {type(raw_text)}")
                    print(f"    Data length: {len(raw_text)}")
                    
                    # Analyze the data
                    if isinstance(raw_text, str):
                        print(f"    First 100 chars: {repr(raw_text[:100])}")
                        
                        # Check if it looks like base64
                        if is_base64(raw_text):
                            print("    🔒 Appears to be base64 encoded")
                            try:
                                decoded = base64.b64decode(raw_text)
                                print(f"    Decoded length: {len(decoded)} bytes")
                                print(f"    Decoded first 50 bytes: {decoded[:50]}")
                            except Exception as e:
                                print(f"    ❌ Base64 decode error: {e}")
                        
                        # Check if it looks like plain text
                        elif is_likely_plaintext(raw_text):
                            print("    📄 Appears to be plain text")
                            print(f"    Preview: {raw_text[:200]}...")
                        
                        # Check for other patterns
                        else:
                            print("    🤔 Unknown format")
                            analyze_text_patterns(raw_text)
                    
                    elif isinstance(raw_text, bytes):
                        print(f"    First 50 bytes: {raw_text[:50]}")
                        try:
                            decoded_str = raw_text.decode('utf-8')
                            print("    ✅ Successfully decoded as UTF-8")
                            print(f"    Preview: {decoded_str[:200]}...")
                        except UnicodeDecodeError:
                            print("    ❌ Not valid UTF-8, likely binary/encrypted")
                
                else:
                    print("    ⚠️  No raw text data")
                
                print("-" * 60)
            
            break  # Only process first database session
            
    except Exception as e:
        print(f"❌ Error investigating data: {e}")


def is_base64(s):
    """Check if string looks like base64"""
    if not s:
        return False
    
    # Remove whitespace
    s = re.sub(r'\s', '', s)
    
    # Check if it matches base64 pattern
    base64_pattern = re.compile(r'^[A-Za-z0-9+/]*={0,2}$')
    
    # Must be multiple of 4 and match pattern
    return len(s) % 4 == 0 and base64_pattern.match(s) and len(s) > 20


def is_likely_plaintext(s):
    """Check if string looks like readable text"""
    if not s:
        return False
    
    # Count printable characters
    printable_count = sum(1 for c in s if c.isprintable() or c.isspace())
    printable_ratio = printable_count / len(s)
    
    # If most characters are printable, likely plain text
    return printable_ratio > 0.8


def analyze_text_patterns(text):
    """Analyze patterns in text to understand format"""
    if not text:
        return
    
    # Check for common encryption/encoding patterns
    patterns = {
        "JSON": text.strip().startswith('{') and text.strip().endswith('}'),
        "XML": text.strip().startswith('<') and '>' in text,
        "URL Encoded": '%' in text and any(c in text for c in '0123456789ABCDEF'),
        "Hex": all(c in '0123456789ABCDEFabcdef' for c in text.replace(' ', '').replace('\n', '')),
        "High Entropy": calculate_entropy(text) > 4.5
    }
    
    detected = [name for name, detected in patterns.items() if detected]
    if detected:
        print(f"    🔍 Detected patterns: {', '.join(detected)}")
    
    # Character distribution
    unique_chars = len(set(text))
    print(f"    📊 Unique characters: {unique_chars}/{len(text)} ({unique_chars/len(text)*100:.1f}%)")


def calculate_entropy(text):
    """Calculate Shannon entropy of text"""
    if not text:
        return 0
    
    from collections import Counter
    import math
    
    # Count character frequencies
    counts = Counter(text)
    length = len(text)
    
    # Calculate entropy
    entropy = 0
    for count in counts.values():
        p = count / length
        if p > 0:
            entropy -= p * math.log2(p)
    
    return entropy


if __name__ == "__main__":
    asyncio.run(investigate_sam_data())
