#!/usr/bin/env python3
"""
Solve Timeout Error - Complete Solution
=======================================

This script provides a complete solution to the ai.kontratar.com:5000 timeout error.
It guides you through the setup process and helps you get the embedding service running.

The original error:
HTTPConnectionPool(host='ai.kontratar.com', port=5000): Read timed out.

This happens because the application tries to connect to ai.kontratar.com:5000 for embeddings,
but this server is not accessible.

Usage:
    python solve_timeout_error.py
"""

import os
import sys
import subprocess
import time
from typing import Dict, Any

def print_header(title: str):
    """Print a formatted header"""
    print("\n" + "="*80)
    print(f" {title}")
    print("="*80)

def print_step(step: int, title: str):
    """Print a formatted step"""
    print(f"\n🔧 Step {step}: {title}")
    print("-" * 60)

def check_file_exists(filepath: str) -> bool:
    """Check if a file exists"""
    return os.path.exists(filepath)

def check_python_package(package: str) -> bool:
    """Check if a Python package is installed"""
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "show", package
        ], capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

def get_user_choice(prompt: str, choices: list) -> str:
    """Get user choice from a list of options"""
    while True:
        print(f"\n{prompt}")
        for i, choice in enumerate(choices, 1):
            print(f"  {i}. {choice}")
        
        try:
            selection = input("\nEnter your choice (number): ").strip()
            index = int(selection) - 1
            if 0 <= index < len(choices):
                return choices[index]
            else:
                print("❌ Invalid choice. Please try again.")
        except ValueError:
            print("❌ Please enter a valid number.")

def update_env_file(provider: str, model: str) -> bool:
    """Update the .env file with new embedding configuration"""
    env_file = ".env"
    
    if not os.path.exists(env_file):
        print(f"❌ {env_file} not found")
        return False
    
    # Read current .env file
    with open(env_file, 'r') as f:
        lines = f.readlines()
    
    # Update or add embedding configuration
    updated_lines = []
    provider_found = False
    model_found = False
    
    for line in lines:
        if line.startswith("EMBEDDING_PROVIDER="):
            updated_lines.append(f"EMBEDDING_PROVIDER={provider}\n")
            provider_found = True
        elif line.startswith("EMBEDDING_MODEL="):
            updated_lines.append(f"EMBEDDING_MODEL={model}\n")
            model_found = True
        else:
            updated_lines.append(line)
    
    # Add missing lines
    if not provider_found:
        updated_lines.append(f"EMBEDDING_PROVIDER={provider}\n")
    if not model_found:
        updated_lines.append(f"EMBEDDING_MODEL={model}\n")
    
    # Write back to file
    with open(env_file, 'w') as f:
        f.writelines(updated_lines)
    
    return True

def main():
    """Main function"""
    print_header("SOLVE TIMEOUT ERROR - COMPLETE SOLUTION")
    
    print("This script will help you solve the embedding service timeout error:")
    print("❌ HTTPConnectionPool(host='ai.kontratar.com', port=5000): Read timed out")
    print("\n✅ We'll set up a local embedding service that works reliably!")
    
    # Step 1: Check current setup
    print_step(1, "Check Current Setup")
    
    config_exists = check_file_exists("config.py")
    env_exists = check_file_exists(".env")
    embedding_server_exists = check_file_exists("flexible_embedding_server.py")
    
    print(f"config.py: {'✅' if config_exists else '❌'}")
    print(f".env file: {'✅' if env_exists else '❌'}")
    print(f"flexible_embedding_server.py: {'✅' if embedding_server_exists else '❌'}")
    
    if not all([config_exists, env_exists, embedding_server_exists]):
        print("\n❌ Some required files are missing. Please ensure you have:")
        print("   - config.py (updated with embedding settings)")
        print("   - .env file (with embedding configuration)")
        print("   - flexible_embedding_server.py (the new embedding server)")
        return
    
    # Step 2: Choose embedding provider
    print_step(2, "Choose Embedding Provider")
    
    providers = {
        "OpenAI (Recommended for production)": {
            "provider": "openai",
            "model": "text-embedding-3-small",
            "requires_key": True,
            "key_env": "OPENAI_API_KEY"
        },
        "Sentence Transformers (Free, local)": {
            "provider": "sentence-transformers", 
            "model": "all-MiniLM-L6-v2",
            "requires_key": False,
            "key_env": None
        },
        "Google Gemini": {
            "provider": "gemini",
            "model": "embedding-001", 
            "requires_key": True,
            "key_env": "GEMINI_API_KEY"
        },
        "Ollama (Local LLM)": {
            "provider": "ollama",
            "model": "nomic-embed-text",
            "requires_key": False,
            "key_env": None
        }
    }
    
    provider_choice = get_user_choice(
        "Which embedding provider would you like to use?",
        list(providers.keys())
    )
    
    selected_provider = providers[provider_choice]
    
    # Step 3: Check API key if required
    if selected_provider["requires_key"]:
        print_step(3, f"Check {selected_provider['key_env']} API Key")
        
        api_key = os.getenv(selected_provider["key_env"])
        if api_key:
            print(f"✅ {selected_provider['key_env']} is set")
        else:
            print(f"❌ {selected_provider['key_env']} is not set")
            print(f"\nPlease add your API key to the .env file:")
            print(f"{selected_provider['key_env']}=your_api_key_here")
            
            if selected_provider["provider"] == "openai":
                print("\nGet your OpenAI API key from: https://platform.openai.com/api-keys")
            elif selected_provider["provider"] == "gemini":
                print("\nGet your Gemini API key from: https://makersuite.google.com/app/apikey")
            
            input("\nPress Enter after you've added your API key to .env...")
    
    # Step 4: Install dependencies
    print_step(4, "Install Dependencies")
    
    required_packages = ["fastapi", "uvicorn", "pydantic", "requests", "loguru"]
    
    if selected_provider["provider"] == "openai":
        required_packages.append("openai")
    elif selected_provider["provider"] == "gemini":
        required_packages.append("google-generativeai")
    elif selected_provider["provider"] == "sentence-transformers":
        required_packages.extend(["sentence-transformers", "torch"])
    
    missing_packages = [pkg for pkg in required_packages if not check_python_package(pkg)]
    
    if missing_packages:
        print(f"Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install"
            ] + missing_packages, check=True)
            print("✅ Dependencies installed successfully")
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            print("Please run manually: pip install " + " ".join(missing_packages))
            return
    else:
        print("✅ All dependencies are already installed")
    
    # Step 5: Update configuration
    print_step(5, "Update Configuration")
    
    if update_env_file(selected_provider["provider"], selected_provider["model"]):
        print("✅ Updated .env file with new embedding configuration")
        print(f"   Provider: {selected_provider['provider']}")
        print(f"   Model: {selected_provider['model']}")
    else:
        print("❌ Failed to update .env file")
        return
    
    # Step 6: Test the setup
    print_step(6, "Start Embedding Server")
    
    print("Starting the embedding server...")
    print("This will run in the background. You can stop it with Ctrl+C")
    print("\nIn a new terminal, you can test it with:")
    print("  python test_embedding_service.py")
    print("\nThen run your original script:")
    print("  python scripts/qdrant_manager.py add-opportunity --opportunity-id vSe1unlCj9")
    
    # Final instructions
    print_header("SOLUTION COMPLETE!")
    
    print("🎉 Your embedding service is now configured!")
    print("\n📋 What was changed:")
    print("   ✅ Updated config.py with embedding API settings")
    print("   ✅ Updated qdrant_manager.py to use configurable URLs")
    print(f"   ✅ Configured .env to use {selected_provider['provider']} provider")
    print("   ✅ Created flexible_embedding_server.py")
    
    print("\n🚀 Next steps:")
    print("   1. Start the embedding server:")
    print("      python start_embedding_server.py")
    print("   2. Test the service:")
    print("      python test_embedding_service.py")
    print("   3. Run your original script:")
    print("      python scripts/qdrant_manager.py add-opportunity --opportunity-id vSe1unlCj9")
    
    print("\n💡 To switch providers later, just update your .env file:")
    print("   EMBEDDING_PROVIDER=sentence-transformers")
    print("   EMBEDDING_MODEL=all-MiniLM-L6-v2")
    
    print("\n📚 For more details, see: EMBEDDING_SETUP.md")
    
    # Ask if user wants to start the server now
    start_now = input("\nWould you like to start the embedding server now? (y/n): ").lower().strip()
    if start_now in ['y', 'yes']:
        print("\n🚀 Starting embedding server...")
        try:
            subprocess.run([sys.executable, "start_embedding_server.py"])
        except KeyboardInterrupt:
            print("\n🛑 Server stopped")
        except Exception as e:
            print(f"\n❌ Error starting server: {e}")

if __name__ == "__main__":
    main()
