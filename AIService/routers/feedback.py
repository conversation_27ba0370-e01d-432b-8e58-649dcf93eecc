from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from database import get_customer_db
from controllers.customer.datametastore_controller import DataMetastoreController
from services.scheduler_service import ProposalSchedulerService
from services.proposal.constructive_criticism import generate_proposal_criticism
from services.proposal.compliance_schemas import ContentCompliance
from services.proposal.proposal_volumes_retrival import ProposalVolumeRetrievalService
from services.proposal.opportunity_context_trigger import OpportunityContextTrigger
from dependencies.auth import get_current_user
from schemas.auth_schemas import CurrentUser
import base64

router = APIRouter(prefix="/proposals", tags=["proposals"])

class ProposalCriticismRequest(BaseModel):
    """Request model for generating proposal criticism"""
    opportunity_id: str
    tenant_id: str
    source: str
    client_short_name: Optional[str]

class ProposalCriticismResponse(BaseModel):
    """Response model for proposal criticism"""
    id: int
    record_identifier: str
    opportunity_id: str
    tenant_id: str
    record_type: str
    original_document_file_name: Optional[str]
    created_date: str
    raw_text_document: Optional[str]
    original_document: Optional[str]

class ReferenceResponse(BaseModel):
    """Response model for stored references"""
    id: int
    record_identifier: str
    record_type: str
    original_document_file_name: Optional[str]
    created_date: str
    raw_text_document: Optional[str]
    original_document: Optional[str]

class ProposalContextSearchRequest(BaseModel):
    """Request model for proposal context web search"""
    opportunity_id: str
    tenant_id: str
    source: str
    organization_name: Optional[str] = None
    additional_context: Optional[str] = None

class ProposalContextSearchResponse(BaseModel):
    """Response model for proposal context web search"""
    opportunity_id: str
    tenant_id: str
    organization_context: str
    search_summary: str
    key_insights: List[str]
    recommended_approach: str

@router.post(
    "/generate-criticism",
    response_model=ProposalCriticismResponse,
    summary="Generate constructive criticism for a proposal"
)
async def generate_proposal_criticism_endpoint(
    request: ProposalCriticismRequest,
    current_user: CurrentUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_customer_db)
):
    """
    Generate constructive criticism for a specific proposal opportunity.

    Args:
        opportunity_id: The ID of the opportunity
        request: Request body containing tenant_id, source, and client_short_name
        db: Database session dependency

    Returns:
        Proposal criticism record

    Raises:
        HTTPException: If required data is missing or an error occurs
    """
    try:
        logger.info(f"Generating proposal criticism for opportunity {request.opportunity_id}, tenant {request.tenant_id}")

        # Initialize services
        scheduler_service = ProposalSchedulerService()
        volume_retrieval_service = ProposalVolumeRetrievalService()

        # Retrieve proposal volumes from review or format queue
        proposal_volumes = await volume_retrieval_service.get_all_volumes_from_review(db, request.tenant_id, request.opportunity_id)
        if not proposal_volumes or all(v is None for v in proposal_volumes):
            proposal_volumes = await volume_retrieval_service.get_all_volumes_from_format(db, request.tenant_id, request.opportunity_id)
            if not proposal_volumes or all(v is None for v in proposal_volumes):
                logger.warning(f"No proposal volumes found for opportunity {request.opportunity_id}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="No proposal volumes found in review or format queue for the specified opportunity"
                )

        # Get content compliance data
        content_compliance_data, _ = await scheduler_service._get_compliance_data(
            request.opportunity_id, request.source
        )

        # Convert content_compliance data to ContentCompliance objects
        content_compliance_objects = []
        if content_compliance_data and isinstance(content_compliance_data, dict) and "content_compliance" in content_compliance_data:
            for compliance_data in content_compliance_data["content_compliance"]:
                content_compliance_objects.append(ContentCompliance(**compliance_data))
        elif content_compliance_data and isinstance(content_compliance_data, list):
            for compliance_data in content_compliance_data:
                if isinstance(compliance_data, dict):
                    content_compliance_objects.append(ContentCompliance(**compliance_data))

        # Generate constructive criticism
        criticism_record_id = await generate_proposal_criticism(
            opportunity_id=request.opportunity_id,
            tenant_id=request.tenant_id,
            source=request.source,
            proposal_volumes=proposal_volumes,
            content_compliance=content_compliance_objects,
        )

        if not criticism_record_id:
            logger.error(f"Failed to generate criticism for opportunity {request.opportunity_id}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate proposal criticism"
            )

        # Retrieve the generated criticism record
        print(f"Record_identifier: {criticism_record_id}")
        record = await DataMetastoreController.get_by_record_identifier(db, criticism_record_id)

        if not record or record.tenant_id != request.tenant_id or record.record_type != "PROPOSAL_FEEDBACK":
            logger.error(f"Generated criticism record not found for opportunity {request.opportunity_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Generated proposal criticism record not found"
            )

        response = ProposalCriticismResponse(
            id=record.id,
            record_identifier=record.record_identifier,
            opportunity_id=request.opportunity_id,
            tenant_id=record.tenant_id,
            record_type=record.record_type,
            original_document_file_name=record.original_document_file_name,
            created_date=record.created_date.isoformat(),
            raw_text_document=record.raw_text_document,
            original_document=(
                base64.b64encode(record.original_document).decode("utf-8")
                if record.original_document else None
            )
        )

        logger.info(f"Successfully generated and retrieved proposal criticism for opportunity {request.opportunity_id}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating proposal criticism for opportunity {request.opportunity_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error while generating proposal criticism: {str(e)}"
        )

@router.get(
    "/{opportunity_id}/criticisms",
    response_model=List[ProposalCriticismResponse],
    summary="Get stored proposal criticisms for an opportunity"
)
async def get_proposal_criticisms(
    opportunity_id: str,
    tenant_id: str,
    source: str,
    version_number: int,
    current_user: CurrentUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_customer_db)
):
    """
    Retrieve all stored proposal criticism reports for a specific opportunity.
    """
    try:
        record_identifier = f"{opportunity_id}_{source}_criticism_v{version_number}"
        logger.info(f"Fetching proposal criticisms for opportunity {opportunity_id}, tenant {tenant_id}")

        record = await DataMetastoreController.get_by_record_identifier(db, record_identifier)

        if not record or record.tenant_id != tenant_id or record.record_type != "PROPOSAL_FEEDBACK":
            logger.warning(f"No valid criticism found for opportunity {opportunity_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No proposal criticisms found for the specified opportunity and tenant"
            )
        
        response = ProposalCriticismResponse(
            id=record.id,
            record_identifier=record.record_identifier,
            opportunity_id=opportunity_id,
            tenant_id=record.tenant_id,
            record_type=record.record_type,
            original_document_file_name=record.original_document_file_name,
            created_date=record.created_date.isoformat(),
            raw_text_document=record.raw_text_document,
            original_document=(
                base64.b64encode(record.original_document).decode("utf-8")
                if record.original_document else None
            )
        )
        
        logger.info(f"Successfully retrieved proposal criticism for opportunity {opportunity_id}")
        return [response]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving proposal criticisms for opportunity {opportunity_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving proposal criticisms"
        )

@router.get(
    "/{opportunity_id}/references",
    response_model=List[ReferenceResponse],
    summary="Get stored references for an opportunity"
)
async def get_opportunity_references(
    opportunity_id: str,
    db: AsyncSession = Depends(get_customer_db)
):
    """
    Retrieve all stored reference documents for a specific opportunity.
    """
    try:
        logger.info(f"Fetching references for opportunity {opportunity_id}")
        
        records = await DataMetastoreController.get_reference_documents(db, opportunity_id)
        
        if not records:
            logger.warning(f"No references found for opportunity {opportunity_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No reference documents found for the specified opportunity"
            )
        
        
        
        response = [
            ReferenceResponse(
                id=record.id,
                record_identifier=record.record_identifier,
                record_type=record.record_type,
                original_document_file_name=record.original_document_file_name,
                created_date=record.created_date.isoformat(),
                raw_text_document=record.raw_text_document,
                original_document=(
                    base64.b64encode(record.original_document).decode("utf-8")
                    if record.original_document else None
                )
            )
            for record in records
        ]
        
        logger.info(f"Successfully retrieved {len(response)} references for opportunity {opportunity_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving references for opportunity {opportunity_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving references"
        )

@router.post(
    "/search-context",
    response_model=ProposalContextSearchResponse,
    summary="Search for organizational context and store in database for proposal generation"
)
async def search_proposal_context(
    request: ProposalContextSearchRequest
):
    """
    Search the web for organizational context about the entity that posted the proposal
    and store the results in the database for use in proposal generation.

    This endpoint triggers deep investigative research and stores the results in the
    appropriate database table (OppsTable for SAM, CustomOppsTable for custom).
    """
    try:
        logger.info(f"Starting context search and storage for opportunity {request.opportunity_id}, tenant {request.tenant_id}")

        # Initialize trigger service
        context_trigger = OpportunityContextTrigger()

        # Trigger context search based on source type
        if request.source.lower() == "sam":
            success = await context_trigger.trigger_context_search_for_sam_opportunity(
                notice_id=request.opportunity_id,
                tenant_id=request.tenant_id,
                organization_name=request.organization_name,
                additional_context=request.additional_context
            )
        elif request.source.lower() == "custom":
            success = await context_trigger.trigger_context_search_for_custom_opportunity(
                opportunity_id=request.opportunity_id,
                tenant_id=request.tenant_id,
                organization_name=request.organization_name,
                additional_context=request.additional_context
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported source type: {request.source}. Supported types: sam, custom"
            )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to trigger organizational context search"
            )

        # Retrieve the stored context from database to return in response
        from services.proposal.organizational_context_service import OrganizationalContextService
        context_service = OrganizationalContextService()

        stored_context = await context_service.get_organizational_context(
            request.opportunity_id, request.tenant_id, request.source
        )

        if stored_context:
            # Return the stored context data
            response = ProposalContextSearchResponse(
                opportunity_id=request.opportunity_id,
                tenant_id=request.tenant_id,
                organization_context=stored_context.get("organization_context", ""),
                search_summary=stored_context.get("search_summary", "Context search completed and stored in database"),
                key_insights=stored_context.get("key_insights", []),
                recommended_approach=stored_context.get("recommended_approach", "")
            )
        else:
            # Fallback response if context retrieval fails
            response = ProposalContextSearchResponse(
                opportunity_id=request.opportunity_id,
                tenant_id=request.tenant_id,
                organization_context="Context search triggered successfully",
                search_summary=f"Organizational context search initiated for opportunity {request.opportunity_id}",
                key_insights=["Context search completed and stored in database"],
                recommended_approach="Organizational context will be automatically used in proposal generation"
            )

        logger.info(f"Successfully triggered and stored context search for opportunity {request.opportunity_id}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during context search for opportunity {request.opportunity_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error during context search: {str(e)}"
        )