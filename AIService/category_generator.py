import asyncio
from controllers.customer.category_controller import CategoryController
from database import get_customer_db
from utils.embedding_model import <PERSON><PERSON><PERSON>r<PERSON>mbeddings
from loguru import logger
from services.llm.llm_factory import get_llm

llm = get_llm(
    temperature=0.3, 
    num_ctx=6000,
    num_predict=8000)
embedding_model = KontratarEmbeddings("http://ai.kontratar.com:5000")

async def process_category(category):
    # Generate description from LLM
    logger.info(f"Processing category: {category.category_name}")

    system_prompt = "You are a helpful assistant that generates concise and clear descriptions for business categories. Your descriptions should be professional, informative, and suitable for business contexts."
    prompt = f"Generate a concise, clear description for the category: '{category.category_name}'."

    logger.info(f"Generating description for category: {category.category_name}")
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": prompt}
    ]
    response = await llm.ainvoke(messages)
    description = response.content if hasattr(response, "content") else str(response)
    if hasattr(description, "content"):
        description = description.content

    logger.info(f"Generated description: {description[:100]}...")

    # Generate embedding
    logger.info(f"Generating embedding for category: {category.category_name}")
    embedding = embedding_model.embed_query(description)
    logger.info(f"Generated embedding with {len(embedding)} dimensions")

    # Update record (open/close DB session just for this)
    logger.info(f"Updating database record for category: {category.category_name}")
    success = False
    async for db in get_customer_db():
        success = await CategoryController.update_by_category_name(
            db, category.category_name, category_description=description, embedding=embedding
        )
        break

    if success:
        logger.info(f"Successfully updated category: {category.category_name}")
    else:
        logger.error(f"Failed to update category: {category.category_name}")

async def main():
    import asyncio

    while True:
        # Open/close DB session just for fetching categories
        async for db in get_customer_db():
            categories = await CategoryController.get_without_description(db, limit=20)
            break
        if not categories:
            print("No more categories without description.")
            return
               # Process categories sequentially
        for cat in categories:
            await process_category(cat)

if __name__ == "__main__":
    asyncio.run(main())