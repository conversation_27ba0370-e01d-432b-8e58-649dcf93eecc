# Embedding Service Setup Guide

This guide explains how to configure and use the flexible embedding service to solve the `ai.kontratar.com:5000` timeout error.

## Problem

The original error occurs because the application tries to connect to `ai.kontratar.com:5000` for embedding services, but this server is not accessible or times out.

## Solution

We've created a flexible embedding service that can use multiple providers and can be configured entirely through environment variables in the `.env` file.

## Quick Start

### 1. Choose Your Provider

Edit your `.env` file and set the embedding provider:

```bash
# For OpenAI (requires API key)
EMBEDDING_PROVIDER=openai
EMBEDDING_MODEL=text-embedding-3-small

# For Google Gemini (requires API key)
EMBEDDING_PROVIDER=gemini
EMBEDDING_MODEL=embedding-001

# For Sentence Transformers (no API key required)
EMBEDDING_PROVIDER=sentence-transformers
EMBEDDING_MODEL=all-MiniLM-L6-v2

# For Ollama (requires local Ollama installation)
EMBEDDING_PROVIDER=ollama
EMBEDDING_MODEL=nomic-embed-text
```

### 2. Set API Keys (if needed)

Add your API keys to the `.env` file:

```bash
# For OpenAI
OPENAI_API_KEY=your_openai_api_key_here

# For Gemini
GEMINI_API_KEY=your_gemini_api_key_here
```

### 3. Start the Embedding Server

```bash
# Simple start (uses .env configuration)
python start_embedding_server.py

# Or start with specific provider
python start_embedding_server.py --provider openai --model text-embedding-3-small

# Show current configuration
python start_embedding_server.py --show-config
```

### 4. Run Your Application

Now run your qdrant_manager.py script:

```bash
python scripts/qdrant_manager.py add-opportunity --opportunity-id vSe1unlCj9
```

## Provider Details

### OpenAI (Recommended for Production)

**Pros:**
- High quality embeddings
- Fast and reliable
- Good documentation

**Cons:**
- Requires API key and costs money
- External dependency

**Setup:**
1. Get API key from https://platform.openai.com/api-keys
2. Add to `.env`: `OPENAI_API_KEY=your_key_here`
3. Set provider: `EMBEDDING_PROVIDER=openai`

**Models:**
- `text-embedding-3-small` (1536 dim, $0.02/1M tokens) - Recommended
- `text-embedding-3-large` (3072 dim, $0.13/1M tokens) - Higher quality
- `text-embedding-ada-002` (1536 dim, $0.10/1M tokens) - Legacy

### Google Gemini

**Pros:**
- High quality embeddings
- Competitive pricing
- Google's latest technology

**Cons:**
- Requires API key
- External dependency

**Setup:**
1. Get API key from https://makersuite.google.com/app/apikey
2. Add to `.env`: `GEMINI_API_KEY=your_key_here`
3. Set provider: `EMBEDDING_PROVIDER=gemini`

**Models:**
- `embedding-001` (768 dimensions) - Recommended
- `text-embedding-004` (768 dimensions) - Latest

### Sentence Transformers (Recommended for Development)

**Pros:**
- No API key required
- Runs locally
- No external dependencies
- Free

**Cons:**
- Slower than cloud APIs
- Requires local compute resources
- Lower quality than OpenAI/Gemini

**Setup:**
1. Install: `pip install sentence-transformers`
2. Set provider: `EMBEDDING_PROVIDER=sentence-transformers`

**Models:**
- `all-MiniLM-L6-v2` (384 dim) - Fast, good for development
- `all-mpnet-base-v2` (768 dim) - Better quality, slower
- `all-MiniLM-L12-v2` (384 dim) - Balanced

### Ollama (Local LLM)

**Pros:**
- Runs locally
- No API key required
- Privacy-focused

**Cons:**
- Requires Ollama installation
- Slower than cloud APIs
- Requires model downloads

**Setup:**
1. Install Ollama: https://ollama.ai/
2. Start Ollama: `ollama serve`
3. Pull model: `ollama pull nomic-embed-text`
4. Set provider: `EMBEDDING_PROVIDER=ollama`

**Models:**
- `nomic-embed-text` (768 dim) - Recommended
- `mxbai-embed-large` (1024 dim) - Higher quality
- `snowflake-arctic-embed` (1024 dim) - Latest

## Configuration Examples

### Example 1: OpenAI Production Setup

```bash
# .env file
EMBEDDING_PROVIDER=openai
EMBEDDING_MODEL=text-embedding-3-small
EMBEDDING_API_HOST=localhost
EMBEDDING_API_PORT=5000
OPENAI_API_KEY=sk-your-key-here
```

### Example 2: Local Development Setup

```bash
# .env file
EMBEDDING_PROVIDER=sentence-transformers
EMBEDDING_MODEL=all-MiniLM-L6-v2
EMBEDDING_API_HOST=localhost
EMBEDDING_API_PORT=5000
```

### Example 3: Ollama Setup

```bash
# .env file
EMBEDDING_PROVIDER=ollama
EMBEDDING_MODEL=nomic-embed-text
EMBEDDING_API_HOST=localhost
EMBEDDING_API_PORT=5000
```

## Troubleshooting

### Server Won't Start

1. Check your API keys are correct
2. For Ollama, ensure `ollama serve` is running
3. For Sentence Transformers, install required packages: `pip install sentence-transformers`
4. Check port 5000 is not already in use

### Timeout Errors

1. Increase timeout in `.env`: `EMBEDDING_API_TIMEOUT=300`
2. For Sentence Transformers, try a smaller model
3. Check your internet connection for cloud APIs

### Quality Issues

1. For better quality, use OpenAI `text-embedding-3-large`
2. For Sentence Transformers, try `all-mpnet-base-v2`
3. Ensure your text preprocessing is consistent

## Switching Providers

You can easily switch providers by updating your `.env` file:

```bash
# Switch to OpenAI
python start_embedding_server.py --provider openai --model text-embedding-3-small --update-env

# Switch to Sentence Transformers
python start_embedding_server.py --provider sentence-transformers --model all-MiniLM-L6-v2 --update-env

# Switch to Gemini
python start_embedding_server.py --provider gemini --model embedding-001 --update-env
```

## Performance Comparison

| Provider | Speed | Quality | Cost | Setup Difficulty |
|----------|-------|---------|------|------------------|
| OpenAI | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 💰💰 | ⭐⭐ |
| Gemini | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 💰 | ⭐⭐ |
| Sentence Transformers | ⭐⭐ | ⭐⭐⭐ | Free | ⭐⭐⭐ |
| Ollama | ⭐⭐⭐ | ⭐⭐⭐ | Free | ⭐⭐⭐⭐ |

## Next Steps

1. Choose your provider based on your needs
2. Update your `.env` file
3. Start the embedding server
4. Test with your qdrant_manager.py script
5. Monitor performance and adjust as needed

For production use, we recommend OpenAI for the best balance of speed, quality, and reliability.
