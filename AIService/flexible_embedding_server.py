#!/usr/bin/env python3
"""
Flexible Embedding Server
=========================

This script provides a flexible embedding service that can be configured entirely through environment variables.
It supports multiple embedding providers and can be easily switched without modifying any code.

Supported Providers:
- OpenAI (text-embedding-3-small, text-embedding-3-large, text-embedding-ada-002)
- Google Gemini (embedding-001, text-embedding-004)
- Sentence Transformers (all-MiniLM-L6-v2, all-mpnet-base-v2, etc.)
- Ollama (nomic-embed-text, mxbai-embed-large, etc.)

Configuration via Environment Variables:
- EMBEDDING_PROVIDER: openai, gemini, sentence-transformers, ollama
- EMBEDDING_MODEL: model name specific to the provider
- EMBEDDING_API_HOST: host to bind the server (default: localhost)
- EMBEDDING_API_PORT: port to bind the server (default: 5000)
- OPENAI_API_KEY: required for OpenAI provider
- GEMINI_API_KEY: required for Gemini provider
- OLLAMA_HOST: Ollama server host (default: localhost)
- OLLAMA_PORT: Ollama server port (default: 11434)

Usage:
    python flexible_embedding_server.py
"""

import asyncio
import os
import sys
from typing import List, Dict, Any, Optional
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn
from loguru import logger

# Add the current directory to the path so we can import config
sys.path.append(os.path.dirname(__file__))
from config import settings

class EmbedRequest(BaseModel):
    texts: List[str]

class EmbedResponse(BaseModel):
    embeddings: List[List[float]]

class FlexibleEmbeddingServer:
    def __init__(self):
        self.provider = settings.embedding_provider.lower()
        self.model = settings.embedding_model
        self.host = settings.embedding_api_host
        self.port = settings.embedding_api_port
        self.timeout = settings.embedding_api_timeout
        self.embedding_client = None
        
        # Initialize FastAPI app
        self.app = FastAPI(
            title="Flexible Embedding Server", 
            version="1.0.0",
            description=f"Embedding service using {self.provider} provider"
        )
        self.setup_routes()
        
    def setup_routes(self):
        """Setup FastAPI routes"""
        
        @self.app.get("/")
        async def root():
            return {
                "message": "Flexible Embedding Server", 
                "provider": self.provider,
                "model": self.model, 
                "status": "running",
                "host": self.host,
                "port": self.port
            }
        
        @self.app.get("/health")
        async def health():
            """Health check endpoint"""
            try:
                if self.embedding_client is None:
                    return {"status": "unhealthy", "error": "Embedding client not initialized"}
                
                # Test with a simple embedding
                test_embedding = await self.get_embedding("health check test")
                if test_embedding and len(test_embedding) > 0:
                    return {
                        "status": "healthy", 
                        "provider": self.provider,
                        "model": self.model,
                        "embedding_dim": len(test_embedding),
                        "test_successful": True
                    }
                else:
                    return {"status": "unhealthy", "error": "Failed to generate test embedding"}
                    
            except Exception as e:
                return {"status": "unhealthy", "error": str(e)}
        
        @self.app.post("/embed_batch", response_model=EmbedResponse)
        async def embed_batch(request: EmbedRequest):
            """Generate embeddings for a batch of texts (compatible with KontratarEmbeddings)"""
            try:
                logger.info(f"Processing {len(request.texts)} texts for embedding using {self.provider}")
                
                embeddings = []
                for i, text in enumerate(request.texts):
                    try:
                        embedding = await self.get_embedding(text)
                        embeddings.append(embedding)
                        if (i + 1) % 10 == 0:
                            logger.info(f"Processed {i + 1}/{len(request.texts)} embeddings")
                    except Exception as e:
                        logger.error(f"Error processing text {i}: {e}")
                        # Return a zero vector as fallback
                        if embeddings:
                            zero_embedding = [0.0] * len(embeddings[0])
                        else:
                            zero_embedding = [0.0] * 1536  # Default OpenAI embedding size
                        embeddings.append(zero_embedding)
                
                logger.info(f"Successfully generated {len(embeddings)} embeddings")
                return EmbedResponse(embeddings=embeddings)
                
            except Exception as e:
                logger.error(f"Error generating embeddings: {e}")
                raise HTTPException(status_code=500, detail=f"Error generating embeddings: {str(e)}")
        
        @self.app.post("/embed")
        async def embed_single(text: str):
            """Generate embedding for a single text"""
            try:
                embedding = await self.get_embedding(text)
                return {"embedding": embedding}
            except Exception as e:
                logger.error(f"Error generating embedding: {e}")
                raise HTTPException(status_code=500, detail=f"Error generating embedding: {str(e)}")
    
    def initialize_client(self):
        """Initialize the embedding client based on provider"""
        try:
            if self.provider == "openai":
                import openai
                api_key = settings.openai_api_key
                if not api_key:
                    raise Exception("OPENAI_API_KEY not configured in settings")
                self.embedding_client = openai.OpenAI(api_key=api_key)
                logger.info(f"Initialized OpenAI client with model: {self.model}")
                
            elif self.provider == "gemini":
                import google.generativeai as genai
                api_key = settings.gemini_api_key
                if not api_key:
                    raise Exception("GEMINI_API_KEY not configured in settings")
                genai.configure(api_key=api_key)
                self.embedding_client = genai
                logger.info(f"Initialized Gemini client with model: {self.model}")
                
            elif self.provider == "sentence-transformers":
                from sentence_transformers import SentenceTransformer
                self.embedding_client = SentenceTransformer(self.model)
                logger.info(f"Initialized Sentence Transformers with model: {self.model}")
                
            elif self.provider == "ollama":
                import requests
                ollama_host = os.getenv("OLLAMA_HOST", "localhost")
                ollama_port = os.getenv("OLLAMA_PORT", "11434")
                self.ollama_url = f"http://{ollama_host}:{ollama_port}"
                
                # Test Ollama connection
                response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
                if response.status_code != 200:
                    raise Exception(f"Cannot connect to Ollama at {self.ollama_url}")
                
                # Check if model is available
                models = response.json().get("models", [])
                model_names = [model.get("name", "").split(":")[0] for model in models]
                if self.model not in model_names:
                    logger.warning(f"Model '{self.model}' not found. Available: {model_names}")
                    logger.info(f"To install: ollama pull {self.model}")
                
                self.embedding_client = "ollama"  # Just a marker
                logger.info(f"Initialized Ollama client with model: {self.model}")
                
            else:
                raise Exception(f"Unsupported provider: {self.provider}")
                
        except ImportError as e:
            raise Exception(f"Required library not installed for {self.provider}: {e}")
        except Exception as e:
            raise Exception(f"Failed to initialize {self.provider} client: {e}")
    
    async def get_embedding(self, text: str) -> List[float]:
        """Get embedding for a single text"""
        try:
            if self.provider == "openai":
                response = self.embedding_client.embeddings.create(
                    model=self.model,
                    input=text
                )
                return response.data[0].embedding
                
            elif self.provider == "gemini":
                result = self.embedding_client.embed_content(
                    model=f"models/{self.model}",
                    content=text
                )
                return result['embedding']
                
            elif self.provider == "sentence-transformers":
                # Run in thread pool to avoid blocking
                import asyncio
                loop = asyncio.get_event_loop()
                embedding = await loop.run_in_executor(
                    None, 
                    self.embedding_client.encode, 
                    text
                )
                return embedding.tolist()
                
            elif self.provider == "ollama":
                import requests
                payload = {
                    "model": self.model,
                    "prompt": text
                }
                
                response = requests.post(
                    f"{self.ollama_url}/api/embeddings",
                    json=payload,
                    timeout=self.timeout
                )
                
                if response.status_code != 200:
                    raise Exception(f"Ollama API returned {response.status_code}: {response.text}")
                
                result = response.json()
                embedding = result.get("embedding")
                
                if not embedding:
                    raise Exception("No embedding returned from Ollama")
                
                return embedding
                
            else:
                raise Exception(f"Unsupported provider: {self.provider}")
                
        except Exception as e:
            raise Exception(f"Error getting embedding with {self.provider}: {e}")
    
    def run(self):
        """Run the embedding server"""
        logger.info("="*80)
        logger.info("FLEXIBLE EMBEDDING SERVER")
        logger.info("="*80)
        logger.info(f"Provider: {self.provider}")
        logger.info(f"Model: {self.model}")
        logger.info(f"Host: {self.host}")
        logger.info(f"Port: {self.port}")
        logger.info(f"Timeout: {self.timeout}s")
        logger.info("="*80)
        
        # Initialize client
        try:
            self.initialize_client()
            logger.info("✅ Embedding client initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize embedding client: {e}")
            logger.error("Check your environment variables and API keys")
            sys.exit(1)
        
        logger.info("🚀 Starting embedding server...")
        logger.info(f"📡 Server available at: http://{self.host}:{self.port}")
        logger.info("📋 API endpoints:")
        logger.info(f"   - GET  /health - Health check")
        logger.info(f"   - POST /embed_batch - Batch embedding (KontratarEmbeddings compatible)")
        logger.info(f"   - POST /embed - Single text embedding")
        logger.info("="*80)
        
        try:
            uvicorn.run(
                self.app,
                host=self.host,
                port=self.port,
                log_level="info"
            )
        except KeyboardInterrupt:
            logger.info("🛑 Server stopped by user")
        except Exception as e:
            logger.error(f"💥 Server error: {e}")
            sys.exit(1)

def main():
    """Main function"""
    server = FlexibleEmbeddingServer()
    server.run()

if __name__ == "__main__":
    main()
