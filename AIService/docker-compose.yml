services:
  aiservice:
    build:
      context: .
      dockerfile: Dockerfile
    image: kontratar/aiservice:${TAG:-v1.2}
    container_name: aiservice-app
    ports:
      - "${APP_PORT}:${APP_PORT}"
    environment:
      - APP_HOST=${APP_HOST}
      - APP_PORT=${APP_PORT}
      - DEBUG=${DEBUG}
      - KONTRATAR_DB_HOST=${KONTRATAR_DB_HOST}
      - KONTRATAR_DB_PORT=${KONTRATAR_DB_PORT}
      - KONTRATAR_DB_NAME=${KONTRATAR_DB_NAME}
      - KONTRATAR_DB_USER=${KONTRATAR_DB_USER}
      - KONTRATAR_DB_PASSWORD=${KONTRATAR_DB_PASSWORD}
      - CUSTOMER_DB_HOST=${CUSTOMER_DB_HOST}
      - CUSTOMER_DB_PORT=${CUSTOMER_DB_PORT}
      - CUSTOMER_DB_NAME=${CUSTOMER_DB_NAME}
      - CUSTOMER_DB_USER=${CUSTOMER_DB_USER}
      - CUSTOMER_DB_PASSWORD=${CUSTOMER_DB_PASSWORD}
      - LANGCHAIN_TRACING_V2=${LANGCHAIN_TRACING_V2}
      - LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY}
      - LANGCHAIN_PROJECT=${LANGCHAIN_PROJECT}
      # Qdrant Configuration
      - QDRANT_HOST=${QDRANT_HOST}
      - QDRANT_PORT=${QDRANT_PORT}
      - QDRANT_GRPC_PORT=${QDRANT_GRPC_PORT}
      - QDRANT_API_KEY=${QDRANT_API_KEY}
      - QDRANT_TIMEOUT=${QDRANT_TIMEOUT}
      - QDRANT_PREFER_GRPC=${QDRANT_PREFER_GRPC}
      # Scheduler Configuration
      - SCHEDULER_ENABLE_ON_STARTUP=${SCHEDULER_ENABLE_ON_STARTUP}
      - SCHEDULER_INTERVAL_SECONDS=${SCHEDULER_INTERVAL_SECONDS}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - LLM_PROVIDER=${LLM_PROVIDER}
      - LLM_MODEL=${LLM_MODEL}
      - LLM_URL=${LLM_URL}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - TZ=${TZ}

    networks:
      - aiservice-network
    restart: unless-stopped

networks:
  aiservice-network:
    driver: bridge
