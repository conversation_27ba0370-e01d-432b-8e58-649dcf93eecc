#!/usr/bin/env python3
"""
Test script to verify that form generation has been properly disabled
and replaced with blank page placeholders.
"""

import asyncio
import sys
import os

# Add the AIService directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.proposal.outline import ProposalOutlineService


async def test_blank_form_placeholders():
    """Test that the new blank form placeholder method works correctly"""
    print("Testing blank form placeholder generation...")
    
    # Initialize the service
    outline_service = ProposalOutlineService()
    
    # Test data
    opportunity_id = "test-opportunity-123"
    tenant_id = "test-tenant-456"
    client_short_name = "Test Client"
    tenant_metadata = "Test Company Metadata"
    
    # Content compliance that should trigger form placeholders
    content_compliance = [
        {
            "volume_title": "Volume 2",
            "description": "Price proposal volume"
        }
    ]
    
    try:
        # Call the new method
        placeholders = await outline_service.generate_blank_form_placeholders(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            content_compliance=content_compliance,
            client_short_name=client_short_name,
            tenant_metadata=tenant_metadata
        )
        
        print(f"✓ Successfully generated {len(placeholders)} form placeholders")
        
        # Verify the placeholders
        for i, placeholder in enumerate(placeholders, 1):
            print(f"\nPlaceholder {i}:")
            print(f"  Form Type: {placeholder.get('form_type')}")
            print(f"  Form Title: {placeholder.get('form_title')}")
            print(f"  Content: {placeholder.get('content')}")
            print(f"  Is Placeholder: {placeholder.get('is_placeholder')}")
            print(f"  Generated At: {placeholder.get('generated_at')}")
            
            # Verify it's a proper placeholder
            assert placeholder.get('is_placeholder') == True, "Should be marked as placeholder"
            assert "[BLANK PAGE" in placeholder.get('content', ''), "Should contain blank page indicator"
            assert "manually" in placeholder.get('content', '').lower(), "Should indicate manual completion"
        
        print("\n✓ All placeholders are properly formatted")
        
        # Test with content compliance that doesn't trigger forms
        content_compliance_no_forms = [
            {
                "volume_title": "Volume 1",
                "description": "Technical proposal volume"
            }
        ]
        
        placeholders_empty = await outline_service.generate_blank_form_placeholders(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            content_compliance=content_compliance_no_forms,
            client_short_name=client_short_name,
            tenant_metadata=tenant_metadata
        )
        
        print(f"✓ No placeholders generated for non-pricing volumes: {len(placeholders_empty)} placeholders")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing blank form placeholders: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_old_methods_removed():
    """Test that the old form generation methods have been removed"""
    print("\nTesting that old form generation methods are removed...")
    
    outline_service = ProposalOutlineService()
    
    # Check that old methods don't exist
    old_methods = [
        'generate_required_forms',
        '_generate_sf1449_form', 
        '_generate_wage_determination_form'
    ]
    
    for method_name in old_methods:
        if hasattr(outline_service, method_name):
            print(f"✗ Old method {method_name} still exists!")
            return False
        else:
            print(f"✓ Old method {method_name} successfully removed")
    
    # Check that new method exists
    if hasattr(outline_service, 'generate_blank_form_placeholders'):
        print("✓ New method generate_blank_form_placeholders exists")
        return True
    else:
        print("✗ New method generate_blank_form_placeholders not found!")
        return False


async def main():
    """Run all tests"""
    print("=" * 60)
    print("TESTING FORM GENERATION FIX")
    print("=" * 60)
    
    # Test 1: Check old methods are removed
    test1_passed = await test_old_methods_removed()
    
    # Test 2: Test new blank placeholder method
    test2_passed = await test_blank_form_placeholders()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS")
    print("=" * 60)
    print(f"Old methods removed: {'✓ PASS' if test1_passed else '✗ FAIL'}")
    print(f"Blank placeholders work: {'✓ PASS' if test2_passed else '✗ FAIL'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED! Form generation has been successfully disabled.")
        print("Forms will now show as blank pages that need to be completed manually.")
        return 0
    else:
        print("\n❌ SOME TESTS FAILED! Please review the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
