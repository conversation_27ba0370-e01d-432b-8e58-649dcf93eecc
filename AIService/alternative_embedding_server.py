#!/usr/bin/env python3
"""
Alternative Embedding Server
============================

This script provides an alternative embedding service that can replace the remote ai.kontratar.com:5000 service.
It supports multiple embedding providers: OpenAI, Google Gemini, and Sentence Transformers.

Usage:
    python alternative_embedding_server.py [--port 5000] [--provider openai] [--model text-embedding-3-small]

Requirements:
    - For OpenAI: openai library and OPENAI_API_KEY environment variable
    - For Gemini: google-generativeai library and GEMINI_API_KEY environment variable  
    - For Sentence Transformers: sentence-transformers library
"""

import asyncio
import argparse
import json
import os
import sys
from typing import List, Dict, Any, Optional
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn
from loguru import logger

class EmbedRequest(BaseModel):
    texts: List[str]

class EmbedResponse(BaseModel):
    embeddings: List[List[float]]

class AlternativeEmbeddingServer:
    def __init__(self, port: int = 5000, provider: str = "sentence-transformers", model: str = "all-MiniLM-L6-v2"):
        self.port = port
        self.provider = provider.lower()
        self.model = model
        self.embedding_client = None
        
        # Initialize FastAPI app
        self.app = FastAPI(title="Alternative Embedding Server", version="1.0.0")
        self.setup_routes()
        
    def setup_routes(self):
        """Setup FastAPI routes"""
        
        @self.app.get("/")
        async def root():
            return {
                "message": "Alternative Embedding Server", 
                "provider": self.provider,
                "model": self.model, 
                "status": "running"
            }
        
        @self.app.get("/health")
        async def health():
            """Health check endpoint"""
            try:
                if self.embedding_client is None:
                    return {"status": "unhealthy", "error": "Embedding client not initialized"}
                
                # Test with a simple embedding
                test_embedding = await self.get_embedding("test")
                if test_embedding and len(test_embedding) > 0:
                    return {
                        "status": "healthy", 
                        "provider": self.provider,
                        "model": self.model,
                        "embedding_dim": len(test_embedding)
                    }
                else:
                    return {"status": "unhealthy", "error": "Failed to generate test embedding"}
                    
            except Exception as e:
                return {"status": "unhealthy", "error": str(e)}
        
        @self.app.post("/embed_batch", response_model=EmbedResponse)
        async def embed_batch(request: EmbedRequest):
            """Generate embeddings for a batch of texts"""
            try:
                logger.info(f"Processing {len(request.texts)} texts for embedding using {self.provider}")
                
                embeddings = []
                for text in request.texts:
                    embedding = await self.get_embedding(text)
                    embeddings.append(embedding)
                
                logger.info(f"Successfully generated {len(embeddings)} embeddings")
                return EmbedResponse(embeddings=embeddings)
                
            except Exception as e:
                logger.error(f"Error generating embeddings: {e}")
                raise HTTPException(status_code=500, detail=f"Error generating embeddings: {str(e)}")
        
        @self.app.post("/embed")
        async def embed_single(text: str):
            """Generate embedding for a single text"""
            try:
                embedding = await self.get_embedding(text)
                return {"embedding": embedding}
            except Exception as e:
                logger.error(f"Error generating embedding: {e}")
                raise HTTPException(status_code=500, detail=f"Error generating embedding: {str(e)}")
    
    def initialize_client(self):
        """Initialize the embedding client based on provider"""
        try:
            if self.provider == "openai":
                import openai
                api_key = os.getenv("OPENAI_API_KEY")
                if not api_key:
                    raise Exception("OPENAI_API_KEY environment variable not set")
                self.embedding_client = openai.OpenAI(api_key=api_key)
                logger.info(f"Initialized OpenAI client with model: {self.model}")
                
            elif self.provider == "gemini":
                import google.generativeai as genai
                api_key = os.getenv("GEMINI_API_KEY")
                if not api_key:
                    raise Exception("GEMINI_API_KEY environment variable not set")
                genai.configure(api_key=api_key)
                self.embedding_client = genai
                logger.info(f"Initialized Gemini client with model: {self.model}")
                
            elif self.provider == "sentence-transformers":
                from sentence_transformers import SentenceTransformer
                self.embedding_client = SentenceTransformer(self.model)
                logger.info(f"Initialized Sentence Transformers with model: {self.model}")
                
            else:
                raise Exception(f"Unsupported provider: {self.provider}")
                
        except ImportError as e:
            raise Exception(f"Required library not installed for {self.provider}: {e}")
        except Exception as e:
            raise Exception(f"Failed to initialize {self.provider} client: {e}")
    
    async def get_embedding(self, text: str) -> List[float]:
        """Get embedding for a single text"""
        try:
            if self.provider == "openai":
                response = self.embedding_client.embeddings.create(
                    model=self.model,
                    input=text
                )
                return response.data[0].embedding
                
            elif self.provider == "gemini":
                result = self.embedding_client.embed_content(
                    model=f"models/{self.model}",
                    content=text
                )
                return result['embedding']
                
            elif self.provider == "sentence-transformers":
                # Sentence transformers can be CPU intensive, so we run in thread pool
                import asyncio
                loop = asyncio.get_event_loop()
                embedding = await loop.run_in_executor(
                    None, 
                    self.embedding_client.encode, 
                    text
                )
                return embedding.tolist()
                
            else:
                raise Exception(f"Unsupported provider: {self.provider}")
                
        except Exception as e:
            raise Exception(f"Error getting embedding with {self.provider}: {e}")
    
    def run(self):
        """Run the embedding server"""
        logger.info("="*60)
        logger.info("ALTERNATIVE EMBEDDING SERVER")
        logger.info("="*60)
        logger.info(f"Port: {self.port}")
        logger.info(f"Provider: {self.provider}")
        logger.info(f"Model: {self.model}")
        logger.info("="*60)
        
        # Initialize client
        try:
            self.initialize_client()
            logger.info("Embedding client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize embedding client: {e}")
            sys.exit(1)
        
        logger.info("Starting embedding server...")
        logger.info(f"Server will be available at: http://localhost:{self.port}")
        logger.info("API endpoints:")
        logger.info(f"  - GET  /health - Health check")
        logger.info(f"  - POST /embed_batch - Batch embedding (compatible with KontratarEmbeddings)")
        logger.info(f"  - POST /embed - Single text embedding")
        
        try:
            uvicorn.run(
                self.app,
                host="0.0.0.0",
                port=self.port,
                log_level="info"
            )
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
        except Exception as e:
            logger.error(f"Server error: {e}")
            sys.exit(1)

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Alternative Embedding Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Use Sentence Transformers (no API key required)
  python alternative_embedding_server.py --provider sentence-transformers --model all-MiniLM-L6-v2

  # Use OpenAI (requires OPENAI_API_KEY)
  python alternative_embedding_server.py --provider openai --model text-embedding-3-small

  # Use Gemini (requires GEMINI_API_KEY)
  python alternative_embedding_server.py --provider gemini --model embedding-001

Installation:
  # For Sentence Transformers
  pip install sentence-transformers

  # For OpenAI
  pip install openai

  # For Gemini
  pip install google-generativeai
        """
    )
    
    parser.add_argument("--port", type=int, default=5000, help="Port to run the server on (default: 5000)")
    parser.add_argument("--provider", choices=["openai", "gemini", "sentence-transformers"], 
                       default="sentence-transformers", help="Embedding provider (default: sentence-transformers)")
    parser.add_argument("--model", help="Model to use (provider-specific)")
    
    args = parser.parse_args()
    
    # Set default models based on provider
    if not args.model:
        if args.provider == "openai":
            args.model = "text-embedding-3-small"
        elif args.provider == "gemini":
            args.model = "embedding-001"
        elif args.provider == "sentence-transformers":
            args.model = "all-MiniLM-L6-v2"
    
    server = AlternativeEmbeddingServer(
        port=args.port,
        provider=args.provider,
        model=args.model
    )
    
    server.run()

if __name__ == "__main__":
    main()
