#!/usr/bin/env python3
"""
Embedding Server Startup Script
===============================

This script helps you start the flexible embedding server with different configurations.
It provides easy switching between providers through command line arguments or environment variables.

Usage:
    # Use environment variables from .env file
    python start_embedding_server.py

    # Override provider via command line
    python start_embedding_server.py --provider openai
    python start_embedding_server.py --provider gemini
    python start_embedding_server.py --provider sentence-transformers

    # Override model via command line
    python start_embedding_server.py --provider openai --model text-embedding-3-large

    # Override host and port
    python start_embedding_server.py --host 0.0.0.0 --port 8080

    # Show current configuration
    python start_embedding_server.py --show-config
"""

import argparse
import os
import sys
from typing import Dict, Any

# Add the current directory to the path
sys.path.append(os.path.dirname(__file__))

def show_configuration():
    """Show current embedding configuration"""
    try:
        from config import settings
        
        print("="*80)
        print("CURRENT EMBEDDING CONFIGURATION")
        print("="*80)
        print(f"Provider: {settings.embedding_provider}")
        print(f"Model: {settings.embedding_model}")
        print(f"Host: {settings.embedding_api_host}")
        print(f"Port: {settings.embedding_api_port}")
        print(f"Timeout: {settings.embedding_api_timeout}s")
        print(f"Embedding API URL: {settings.embedding_api_url}")
        print()
        print("API Keys Status:")
        print(f"  OpenAI API Key: {'✅ Set' if settings.openai_api_key else '❌ Not set'}")
        print(f"  Gemini API Key: {'✅ Set' if settings.gemini_api_key else '❌ Not set'}")
        print()
        print("Provider-specific Information:")
        
        if settings.embedding_provider == "openai":
            print("  OpenAI Models:")
            print("    - text-embedding-3-small (1536 dimensions, $0.02/1M tokens)")
            print("    - text-embedding-3-large (3072 dimensions, $0.13/1M tokens)")
            print("    - text-embedding-ada-002 (1536 dimensions, $0.10/1M tokens)")
            
        elif settings.embedding_provider == "gemini":
            print("  Gemini Models:")
            print("    - embedding-001 (768 dimensions)")
            print("    - text-embedding-004 (768 dimensions)")
            
        elif settings.embedding_provider == "sentence-transformers":
            print("  Popular Sentence Transformer Models:")
            print("    - all-MiniLM-L6-v2 (384 dimensions, fast)")
            print("    - all-mpnet-base-v2 (768 dimensions, better quality)")
            print("    - all-MiniLM-L12-v2 (384 dimensions, balanced)")
            
        elif settings.embedding_provider == "ollama":
            print("  Popular Ollama Embedding Models:")
            print("    - nomic-embed-text (768 dimensions)")
            print("    - mxbai-embed-large (1024 dimensions)")
            print("    - snowflake-arctic-embed (1024 dimensions)")
            
        print("="*80)
        
    except Exception as e:
        print(f"Error loading configuration: {e}")
        sys.exit(1)

def update_env_file(provider: str = None, model: str = None, host: str = None, port: int = None):
    """Update .env file with new configuration"""
    env_file = ".env"
    
    if not os.path.exists(env_file):
        print(f"Error: {env_file} not found")
        return False
    
    # Read current .env file
    with open(env_file, 'r') as f:
        lines = f.readlines()
    
    # Update lines
    updated = False
    for i, line in enumerate(lines):
        if provider and line.startswith("EMBEDDING_PROVIDER="):
            lines[i] = f"EMBEDDING_PROVIDER={provider}\n"
            updated = True
        elif model and line.startswith("EMBEDDING_MODEL="):
            lines[i] = f"EMBEDDING_MODEL={model}\n"
            updated = True
        elif host and line.startswith("EMBEDDING_API_HOST="):
            lines[i] = f"EMBEDDING_API_HOST={host}\n"
            updated = True
        elif port and line.startswith("EMBEDDING_API_PORT="):
            lines[i] = f"EMBEDDING_API_PORT={port}\n"
            updated = True
    
    # Add missing lines if they don't exist
    if provider and not any(line.startswith("EMBEDDING_PROVIDER=") for line in lines):
        lines.append(f"EMBEDDING_PROVIDER={provider}\n")
        updated = True
    if model and not any(line.startswith("EMBEDDING_MODEL=") for line in lines):
        lines.append(f"EMBEDDING_MODEL={model}\n")
        updated = True
    
    if updated:
        # Write back to .env file
        with open(env_file, 'w') as f:
            f.writelines(lines)
        print(f"✅ Updated {env_file}")
        return True
    
    return False

def validate_configuration(provider: str, model: str):
    """Validate provider and model combination"""
    valid_combinations = {
        "openai": [
            "text-embedding-3-small",
            "text-embedding-3-large", 
            "text-embedding-ada-002"
        ],
        "gemini": [
            "embedding-001",
            "text-embedding-004"
        ],
        "sentence-transformers": [
            "all-MiniLM-L6-v2",
            "all-mpnet-base-v2",
            "all-MiniLM-L12-v2",
            "paraphrase-MiniLM-L6-v2",
            "distilbert-base-nli-stsb-mean-tokens"
        ],
        "ollama": [
            "nomic-embed-text",
            "mxbai-embed-large", 
            "snowflake-arctic-embed"
        ]
    }
    
    if provider not in valid_combinations:
        print(f"❌ Invalid provider: {provider}")
        print(f"Valid providers: {list(valid_combinations.keys())}")
        return False
    
    # For sentence-transformers and ollama, we allow any model name
    if provider in ["sentence-transformers", "ollama"]:
        return True
    
    if model not in valid_combinations[provider]:
        print(f"❌ Invalid model '{model}' for provider '{provider}'")
        print(f"Valid models for {provider}: {valid_combinations[provider]}")
        return False
    
    return True

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Embedding Server Startup Script",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Show current configuration
  python start_embedding_server.py --show-config

  # Start with OpenAI
  python start_embedding_server.py --provider openai --model text-embedding-3-small

  # Start with Gemini
  python start_embedding_server.py --provider gemini --model embedding-001

  # Start with Sentence Transformers (no API key required)
  python start_embedding_server.py --provider sentence-transformers --model all-MiniLM-L6-v2

  # Start with Ollama (requires Ollama running locally)
  python start_embedding_server.py --provider ollama --model nomic-embed-text

Environment Variables:
  EMBEDDING_PROVIDER: openai, gemini, sentence-transformers, ollama
  EMBEDDING_MODEL: model name specific to provider
  EMBEDDING_API_HOST: server host (default: localhost)
  EMBEDDING_API_PORT: server port (default: 5000)
  OPENAI_API_KEY: required for OpenAI
  GEMINI_API_KEY: required for Gemini
        """
    )
    
    parser.add_argument("--provider", choices=["openai", "gemini", "sentence-transformers", "ollama"],
                       help="Embedding provider")
    parser.add_argument("--model", help="Embedding model")
    parser.add_argument("--host", help="Server host")
    parser.add_argument("--port", type=int, help="Server port")
    parser.add_argument("--show-config", action="store_true", help="Show current configuration and exit")
    parser.add_argument("--update-env", action="store_true", help="Update .env file with provided arguments")
    
    args = parser.parse_args()
    
    if args.show_config:
        show_configuration()
        return
    
    # Validate arguments
    if args.provider and args.model:
        if not validate_configuration(args.provider, args.model):
            sys.exit(1)
    
    # Update .env file if requested
    if args.update_env:
        updated = update_env_file(args.provider, args.model, args.host, args.port)
        if updated:
            print("✅ Configuration updated in .env file")
        else:
            print("ℹ️  No changes made to .env file")
    
    # Set environment variables for this session
    if args.provider:
        os.environ["EMBEDDING_PROVIDER"] = args.provider
    if args.model:
        os.environ["EMBEDDING_MODEL"] = args.model
    if args.host:
        os.environ["EMBEDDING_API_HOST"] = args.host
    if args.port:
        os.environ["EMBEDDING_API_PORT"] = str(args.port)
    
    # Show configuration before starting
    print("Starting embedding server with configuration:")
    show_configuration()
    
    # Start the server
    try:
        from flexible_embedding_server import FlexibleEmbeddingServer
        server = FlexibleEmbeddingServer()
        server.run()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"💥 Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
