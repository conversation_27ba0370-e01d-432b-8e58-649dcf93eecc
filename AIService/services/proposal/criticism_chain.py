'''
This service is used to criticize the proposal outline and ensure that it follows all the 4 compliance steps:
- generate_technical_requirements
- generate_content_compliance  
- generate_structure_compliance
- generate_formatting_requirements
'''

import json
from typing import List
from services.proposal.utilities import ProposalUtilities
from services.llm.llm_factory import get_llm
from pydantic import BaseModel, Field

# Pydantic schemas for structured LLM output
class OutputSchema(BaseModel):
    requirement_name: str = Field(description="Name of the requirement")
    score: int = Field(description="Score from 0 to 100")
    criticisms: List[str] = Field(description="List of issues found")
    strengths: List[str] = Field(description="List of strengths found")


class CriticismChain:
    """
    CriticismChain provides intelligent analysis of proposal outlines against compliance requirements.
    It analyzes structure, content, formatting, and technical aspects using AI-powered criticism.
    """
    
    def __init__(self, llm_api_url: str = "http://ai.kontratar.com:11434"):
        """Initialize the criticism chain with LLM and utilities."""
        self.utilities = ProposalUtilities()
        
        # Initialize LLM for intelligent criticism with enhanced configuration
        self.llm = get_llm(
            temperature=0.05,  # Slightly higher for more nuanced analysis
            timeout=180,       # Increased timeout for thorough analysis
            num_ctx=8192,      # Larger context window for comprehensive evaluation
            num_predict=2048,  # More detailed criticism and recommendations
            base_url=llm_api_url  # For backward compatibility with Ollama
        )
        
        # Create structured output models for each analysis type
        self.structure_model = self.llm.with_structured_output(OutputSchema)
        self.content_model = self.llm.with_structured_output(OutputSchema)
        self.formatting_model = self.llm.with_structured_output(OutputSchema)
        self.technical_model = self.llm.with_structured_output(OutputSchema)


    def criticize_structure(self, proposal_outline: dict, structure_compliance: dict) -> dict:
        """Criticize structure compliance only using LLM."""
        llm_structure_criticism = self._llm_criticize_structure_compliance(proposal_outline, structure_compliance)
        
        return {
            "score": llm_structure_criticism.get('score', 0),
            "strengths": llm_structure_criticism.get('strengths', []),
            "issues": llm_structure_criticism.get('criticisms', []),
            "llm_analysis": llm_structure_criticism
        }

    def criticize_content(self, proposal_outline: dict, content_compliance: dict) -> dict:
        """Criticize content compliance only using LLM."""
        llm_content_criticism = self._llm_criticize_content_compliance(proposal_outline, content_compliance)
        
        return {
            "score": llm_content_criticism.get('score', 0),
            "strengths": llm_content_criticism.get('strengths', []),
            "issues": llm_content_criticism.get('criticisms', []),
            "llm_analysis": llm_content_criticism
        }

    def criticize_formatting(self, proposal_outline: dict, formatting_requirements: dict) -> dict:
        """Criticize formatting compliance only using LLM."""
        llm_formatting_criticism = self._llm_criticize_formatting_compliance(proposal_outline, formatting_requirements)
        
        return {
            "score": llm_formatting_criticism.get('score', 0),
            "strengths": llm_formatting_criticism.get('strengths', []),
            "issues": llm_formatting_criticism.get('criticisms', []),
            "llm_analysis": llm_formatting_criticism
        }

    def criticize_technical(self, proposal_outline: dict, technical_requirements: dict) -> dict:
        """Criticize technical compliance only using LLM."""
        llm_technical_criticism = self._llm_criticize_technical_compliance(proposal_outline, technical_requirements)
        
        return {
            "score": llm_technical_criticism.get('score', 0),
            "strengths": llm_technical_criticism.get('strengths', []),
            "issues": llm_technical_criticism.get('criticisms', []),
            "llm_analysis": llm_technical_criticism
        }


    def create_structure_report(self, proposal_outline: dict, structure_compliance: dict) -> str:
        """Create a detailed structure criticism report."""
        structure_analysis = self.criticize_structure(proposal_outline, structure_compliance)
        
        report = f"""
# Structure Compliance Report

## Overall Score: {structure_analysis['score']}/100

## Analysis Summary
This report analyzes the proposal outline's structure against the required compliance standards.

## Strengths
"""
        for strength in structure_analysis['strengths']:
            report += f"- {strength}\n"
        
        report += "\n## Issues Found\n"
        for issue in structure_analysis['issues']:
            report += f"- {issue}\n"
        
        report += "\n## Recommendations\n"
        if structure_analysis['score'] >= 80:
            report += "- Structure is well-organized and compliant\n"
            report += "- Minor improvements may be needed\n"
        elif structure_analysis['score'] >= 60:
            report += "- Structure needs moderate improvements\n"
            report += "- Focus on missing sections and organization\n"
        else:
            report += "- Structure requires significant improvements\n"
            report += "- Consider complete reorganization\n"
        
        return report

    def create_content_report(self, proposal_outline: dict, content_compliance: dict) -> str:
        """Create a detailed content criticism report."""
        content_analysis = self.criticize_content(proposal_outline, content_compliance)
        
        report = f"""
# Content Compliance Report

## Overall Score: {content_analysis['score']}/100

## Analysis Summary
This report analyzes the proposal outline's content against the required messaging and theme requirements.

## Strengths
"""
        for strength in content_analysis['strengths']:
            report += f"- {strength}\n"
        
        report += "\n## Issues Found\n"
        for issue in content_analysis['issues']:
            report += f"- {issue}\n"
        
        report += "\n## Recommendations\n"
        if content_analysis['score'] >= 80:
            report += "- Content is well-aligned with requirements\n"
            report += "- Minor messaging adjustments may be needed\n"
        elif content_analysis['score'] >= 60:
            report += "- Content needs moderate improvements\n"
            report += "- Focus on key themes and messaging\n"
        else:
            report += "- Content requires significant improvements\n"
            report += "- Consider complete content strategy revision\n"
        
        return report

    def create_formatting_report(self, proposal_outline: dict, formatting_requirements: dict) -> str:
        """Create a detailed formatting criticism report."""
        formatting_analysis = self.criticize_formatting(proposal_outline, formatting_requirements)
        
        report = f"""
# Formatting Compliance Report

## Overall Score: {formatting_analysis['score']}/100

## Analysis Summary
This report analyzes the proposal outline's formatting against the required presentation standards.

## Strengths
"""
        for strength in formatting_analysis['strengths']:
            report += f"- {strength}\n"
        
        report += "\n## Issues Found\n"
        for issue in formatting_analysis['issues']:
            report += f"- {issue}\n"
        
        report += "\n## Recommendations\n"
        if formatting_analysis['score'] >= 80:
            report += "- Formatting is professional and compliant\n"
            report += "- Minor presentation improvements may be needed\n"
        elif formatting_analysis['score'] >= 60:
            report += "- Formatting needs moderate improvements\n"
            report += "- Focus on consistency and readability\n"
        else:
            report += "- Formatting requires significant improvements\n"
            report += "- Consider complete formatting overhaul\n"
        
        return report

    def create_technical_report(self, proposal_outline: dict, technical_requirements: dict) -> str:
        """Create a detailed technical criticism report."""
        technical_analysis = self.criticize_technical(proposal_outline, technical_requirements)
        
        report = f"""
# Technical Compliance Report

## Overall Score: {technical_analysis['score']}/100

## Analysis Summary
This report analyzes the proposal outline's technical approach against the required technical specifications.

## Strengths
"""
        for strength in technical_analysis['strengths']:
            report += f"- {strength}\n"
        
        report += "\n## Issues Found\n"
        for issue in technical_analysis['issues']:
            report += f"- {issue}\n"
        
        report += "\n## Recommendations\n"
        if technical_analysis['score'] >= 80:
            report += "- Technical approach is well-aligned with requirements\n"
            report += "- Minor technical refinements may be needed\n"
        elif technical_analysis['score'] >= 60:
            report += "- Technical approach needs moderate improvements\n"
            report += "- Focus on technical depth and feasibility\n"
        else:
            report += "- Technical approach requires significant improvements\n"
            report += "- Consider complete technical strategy revision\n"
        
        return report
    
    def generate_full_report(self, proposal_outline: dict, structure_compliance: dict, 
                           content_compliance: dict, formatting_requirements: dict, 
                           technical_requirements: dict) -> str:
        """Generate comprehensive full report from all 4 criticism aspects."""
        # Get individual analyses
        structure = self.criticize_structure(proposal_outline, structure_compliance)
        content = self.criticize_content(proposal_outline, content_compliance)
        formatting = self.criticize_formatting(proposal_outline, formatting_requirements)
        technical = self.criticize_technical(proposal_outline, technical_requirements)
        
        # Calculate overall score
        scores = [structure['score'], content['score'], formatting['score'], technical['score']]
        overall_score = sum(scores) // len(scores)
        
        # Determine overall grade
        if overall_score >= 90:
            grade = "A+"
            grade_description = "Excellent - Proposal is highly compliant"
        elif overall_score >= 80:
            grade = "A"
            grade_description = "Very Good - Proposal meets most requirements"
        elif overall_score >= 70:
            grade = "B"
            grade_description = "Good - Proposal needs some improvements"
        elif overall_score >= 60:
            grade = "C"
            grade_description = "Fair - Proposal needs moderate improvements"
        elif overall_score >= 50:
            grade = "D"
            grade_description = "Poor - Proposal needs significant improvements"
        else:
            grade = "F"
            grade_description = "Failing - Proposal needs major overhaul"
        
        # Build comprehensive report
        report = f"""
# Complete Proposal Criticism Report

## Executive Summary
**Overall Grade: ({overall_score}/100)**
{grade_description}

## Individual Scores Breakdown:
- **Structure:** {structure['score']}/100
- **Content:** {content['score']}/100  
- **Formatting:** {formatting['score']}/100
- **Technical:** {technical['score']}/100

## Detailed Analysis

### 1. Structure Analysis
**Score: {structure['score']}/100**

**Strengths:**
"""
        for strength in structure['strengths']:
            report += f"- {strength}\n"
        
        report += "\n**Issues:**\n"
        for issue in structure['issues']:
            report += f"- {issue}\n"
        
        
        report += f"""
### 2. Content Analysis
**Score: {content['score']}/100**

**Strengths:**
"""
        for strength in content['strengths']:
            report += f"- {strength}\n"
        
        report += "\n**Issues:**\n"
        for issue in content['issues']:
            report += f"- {issue}\n"
        
        
        report += f"""
### 3. Formatting Analysis
**Score: {formatting['score']}/100**

**Strengths:**
"""
        for strength in formatting['strengths']:
            report += f"- {strength}\n"
        
        report += "\n**Issues:**\n"
        for issue in formatting['issues']:
            report += f"- {issue}\n"
        
        
        report += f"""
### 4. Technical Analysis
**Score: {technical['score']}/100**

**Strengths:**
"""
        for strength in technical['strengths']:
            report += f"- {strength}\n"
        
        report += "\n**Issues:**\n"
        for issue in technical['issues']:
            report += f"- {issue}\n"
        
        
        report += f"""
## Priority Recommendations

### High Priority (Score < 60)
"""
        if structure['score'] < 60:
            report += f"- **Structure:** Requires immediate attention - {structure['score']}/100\n"
        if content['score'] < 60:
            report += f"- **Content:** Requires immediate attention - {content['score']}/100\n"
        if formatting['score'] < 60:
            report += f"- **Formatting:** Requires immediate attention - {formatting['score']}/100\n"
        if technical['score'] < 60:
            report += f"- **Technical:** Requires immediate attention - {technical['score']}/100\n"
        
        report += "\n### Medium Priority (Score 60-79)\n"
        if 60 <= structure['score'] < 80:
            report += f"- **Structure:** Needs improvement - {structure['score']}/100\n"
        if 60 <= content['score'] < 80:
            report += f"- **Content:** Needs improvement - {content['score']}/100\n"
        if 60 <= formatting['score'] < 80:
            report += f"- **Formatting:** Needs improvement - {formatting['score']}/100\n"
        if 60 <= technical['score'] < 80:
            report += f"- **Technical:** Needs improvement - {technical['score']}/100\n"
        
        report += "\n### Low Priority (Score ≥ 80)\n"
        if structure['score'] >= 80:
            report += f"- **Structure:** Good - {structure['score']}/100\n"
        if content['score'] >= 80:
            report += f"- **Content:** Good - {content['score']}/100\n"
        if formatting['score'] >= 80:
            report += f"- **Formatting:** Good - {formatting['score']}/100\n"
        if technical['score'] >= 80:
            report += f"- **Technical:** Good - {technical['score']}/100\n"
        
        report += f"""
## Next Steps
Based on the analysis above, focus on the high priority areas first. Each area should be addressed systematically to improve the overall proposal quality and compliance.

"""
        
        return report


    def _llm_criticize_structure_compliance(self, proposal_outline: dict, structure_compliance: dict) -> dict:
        """Use LLM to provide intelligent criticism of structure compliance."""
        try:
            system_prompt = """You are a senior government proposal structure expert with 20+ years of experience. 
            Analyze the proposal outline's structure against the requirements and provide comprehensive 
            feedback on organization, flow, completeness, and scoring optimization opportunities."""

            user_prompt = f"""Analyze this proposal outline's structure compliance:

PROPOSAL OUTLINE:
{json.dumps(proposal_outline, indent=2)}

STRUCTURE REQUIREMENTS:
{json.dumps(structure_compliance, indent=2)}

Provide a comprehensive analysis of the structure compliance."""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # Use structured output model
            analysis = self.structure_model.invoke(messages)
            return analysis.model_dump()
                
        except Exception as e:
            print(f"LLM structure criticism failed: {e}")
            # Re-raise the exception to be handled by the scheduler
            raise

    def _llm_criticize_content_compliance(self, proposal_outline: dict, content_compliance: dict) -> dict:
        """Use LLM to provide intelligent criticism of content compliance."""
        try:
            content_text = content_compliance.get("content", "")
            
            system_prompt = """You are a senior government proposal content expert with 20+ years of experience. 
            Analyze the proposal outline's content against the requirements and provide comprehensive 
            feedback on messaging, key themes, content strategy, and scoring optimization opportunities."""

            user_prompt = f"""Analyze this proposal outline's content compliance:

PROPOSAL OUTLINE:
{json.dumps(proposal_outline, indent=2)}

CONTENT REQUIREMENTS:
{content_text[:2000]}

Provide a comprehensive analysis of the content compliance."""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # Use structured output model
            analysis = self.content_model.invoke(messages)
            return analysis.model_dump()
                
        except Exception as e:
            print(f"LLM content criticism failed: {e}")
            # Re-raise the exception to be handled by the scheduler
            raise

    def _llm_criticize_formatting_compliance(self, proposal_outline: dict, formatting_requirements: dict) -> dict:
        """Use LLM to provide intelligent criticism of formatting compliance."""
        try:
            system_prompt = """You are an expert in government proposal formatting and presentation. 
            Analyze the proposal outline's formatting against the requirements and provide intelligent 
            feedback on presentation, readability, and professional appearance."""

            user_prompt = f"""Analyze this proposal outline's formatting compliance:

PROPOSAL OUTLINE:
{json.dumps(proposal_outline, indent=2)}

FORMATTING REQUIREMENTS:
{json.dumps(formatting_requirements, indent=2)}

Provide a comprehensive analysis of the formatting compliance."""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # Use structured output model
            analysis = self.formatting_model.invoke(messages)
            return analysis.model_dump()
                
        except Exception as e:
            print(f"LLM formatting criticism failed: {e}")
            return {
                "score": 0, 
                "criticisms": [], 
                "strengths": [], 
                "formatting_issues": [], 
                "presentation_suggestions": [], 
                "formatting_quality": "poor"
            }

    def _llm_criticize_technical_compliance(self, proposal_outline: dict, technical_requirements: dict) -> dict:
        """Use LLM to provide intelligent criticism of technical requirements alignment."""
        try:
            tech_content = technical_requirements.get("content", "")
            
            system_prompt = """You are an expert in technical proposal requirements and solution architecture. 
            Analyze the proposal outline's technical approach against the requirements and provide intelligent 
            feedback on technical depth, feasibility, and innovation."""

            user_prompt = f"""Analyze this proposal outline's technical compliance:

PROPOSAL OUTLINE:
{json.dumps(proposal_outline, indent=2)}

TECHNICAL REQUIREMENTS:
{tech_content[:2000]}

Provide a comprehensive analysis of the technical compliance."""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # Use structured output model
            analysis = self.technical_model.invoke(messages)
            return analysis.model_dump()
                
        except Exception as e:
            print(f"LLM technical criticism failed: {e}")
            return {
                "score": 0, 
                "criticisms": [], 
                "strengths": [], 
                "technical_depth": "unknown", 
                "innovation_opportunities": [], 
                "feasibility_concerns": [], 
                "technical_quality": "poor"
            }