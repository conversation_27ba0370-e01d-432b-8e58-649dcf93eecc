from typing import Optional
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from controllers.kontratar.ebuy_opps_controller import EBUYOppsController
from controllers.kontratar.opps_table_controller import OppsTableController

# from langchain_ollama import ChatOllama
from services.chroma.chroma_service import ChromaService
from services.llm.llm_factory import get_llm


class SummaryService:
    """
    Service for generating and storing opportunity summaries using ChatOllama and the appropriate DB table.
    """
    def __init__(self, llm_api_url: str = "http://ai.kontratar.com:11434", model: str = "gemma3:27b"):
        self.llm = get_llm(base_url=llm_api_url, num_predict=1500)
        self.chroma_service = ChromaService(embedding_api_url="http://ai.kontratar.com:5000")

    async def generate_summary(
        self,
        db,
        tenant_id: str,
        opportunity_id: str,
        source: str,
    ) -> str:
        """
        Generate a summary for the opportunity using ChatOllama and relevant ChromaDB chunks.
        """

        chroma_query: str = "Provide a summary of the main objectives, requirements, and unique aspects of this opportunity."
        
        # Get relevant context from ChromaDB
        if source.lower() == "custom":
            chroma_collection = f"{tenant_id}_{opportunity_id}"
        else:
            chroma_collection = opportunity_id
        relevant_chunks = await self.chroma_service.get_relevant_chunks(
            db, chroma_collection, chroma_query, n_results=10
        )
        context = "\n".join(relevant_chunks) if relevant_chunks else ""

        system_prompt = '''
        **Role:**
        Government Solicitation Opportunity Summary Generator

        **Task:**
        Analyze the provided context from an RFI/RFP and generate a concise, compliance-ready summary. The summary should capture the main objectives, requirements, and any unique aspects of the opportunity. Use clear, professional language suitable for government contracting.

        **Rules:**
        1. Use only the information provided in <context>.
        2. The summary should be 1-2 paragraphs, clear and actionable.
        3. Highlight any unique or critical requirements if present.
        4. Do not include any information not found in <context>.

        **Important:**
        ONLY return the summary text, do not return any other information or formatting.
        '''
        user_prompt = f'''
        <context>
        {context}
        </context>
        '''
        messages = [
            {"role": "system", "content": system_prompt.strip()},
            {"role": "user", "content": user_prompt.strip()},
        ]
        response = await self.llm.ainvoke(messages)
        return getattr(response, "content", str(response))

    @staticmethod
    async def get_summary(db, tenant_id: str, opportunity_id: str, source: str) -> Optional[str]:
        source = source.lower()
        if source == "sam":
            record = await OppsTableController.get_by_notice_id(db, opportunity_id)
            return getattr(record, "summary_text", None) if record else None
        elif source == "ebuy":
            record = await EBUYOppsController.get_by_rfq_id(db, opportunity_id)
            return getattr(record, "summary_text", None) if record else None
        elif source == "custom":
            record = await CustomOpportunitiesController.get_by_opportunity_id(db, opportunity_id)
            return getattr(record, "summary_text", None) if record else None
        else:
            raise ValueError("Invalid Opportunity Source")

    @staticmethod
    async def set_summary(db, tenant_id: str, opportunity_id: str, source: str, summary: str):
        source = source.lower()
        update_fields = {"summary_text": summary}
        if source == "sam":
            await OppsTableController.update_by_notice_id(db, opportunity_id, update_fields)
        elif source == "ebuy":
            await EBUYOppsController.update_by_rfq_id(db, opportunity_id, update_fields)
        elif source == "custom":
            await CustomOpportunitiesController.update_by_opportunity_id(db, opportunity_id, update_fields)
        else:
            raise ValueError("Invalid Opportunity Source") 