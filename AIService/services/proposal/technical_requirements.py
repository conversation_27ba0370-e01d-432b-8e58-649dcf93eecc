import asyncio
from typing import Any, Dict, List, Optional
from services.chroma.chroma_service import ChromaService
from utils.llm import KontratarLLM
from database import get_kontratar_db
from loguru import logger
from services.llm.llm_factory import get_llm

class TechnicalRequirementsService:
    """
    Service for generating technical requirements context and LLM output using ChromaDB and an LLM.
    """
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:8080",
    ):
        self.chroma_service = ChromaService(embedding_api_url, None)
        self.llm = get_llm(
            llm_api_url=llm_api_url,
            temperature=0.1,
            timeout=300,
            num_ctx=8192,
            num_predict=2048
        )

    async def generate_technical_requirements(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        max_tokens: int = 2048,
    ) -> Dict[str, Any]:
        """
        Generate technical requirements output using ChromaDB and LLM.
        Returns a dict with 'content' (LLM output) and 'context' (list of cleaned chunks).
        """
        chroma_query = '''
        List all the specific requirements outlined or provided & retrieve all
        responsibilities the contractor shall do and support the contractor shall
        provide with neccessary skills needed. If any technical requirements or contraints, return them as well.
        '''
        # Prepare variables to hold the context
        requirements_context = []
        context_str = ""
        # 1. Do all DB/Chroma work inside the async for block
        async for db in get_kontratar_db():
            max_chunks = 10
            if source.lower() == "custom":
                chroma_collection = f"{tenant_id}_{opportunity_id}"
            else:
                chroma_collection = opportunity_id
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, chroma_collection, chroma_query, n_results=max_chunks
            )
            logger.debug(f"Relevant chunks: {relevant_chunks}")
            requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            context_str = "\n".join(requirements_context)
            break  # Now the session will close

        # 2. Now the DB session is closed, do the LLM call
        system_prompt = '''
            **Role:**
            Government Solicitation Technical Requirements Generator

            **Task**:
            Your task is to anaylze the information from an RFI/RFP related to requirements and produce
            a list of 8 - 10 distinct, compliance-ready requirements. If teh requirements are EXPLICITLY
            and SPECIFICALLY stated in <context>, USE THAT otherwise infer the requirements FROM the information
            given in <context>

            **Rules**:
            1. Review the context provided in <context> and use that as the guide for
            generating the requirements.
            2. Diversity Enforcement:
                - Each requirement must target a unique functional area (e.g. personnel, technology, reporting, training, etc)
                - Ban overlaps: If Requirement 1 covers "staff certifications," no other requirement can focus on certifications.
            3. Mentioned requirements:
                - If the information in <context> mentions any requirements explicitly, that requirement must have a higher weight.

            **Important**:
            The table of contents SHOULD be returned as JSON so ensure to generate accurate, parsebale and valid JSON.
            You will be given a JSON schema to comply to, ensure to follow it strictly.
        '''
        user_prompt = '''
            <context>
                {context_str}
            </context>

            Use the JSON schema below:
            {{ "technicalRequirements": [{{ "requirement": "string", "weight": "string", "explanation": "string" }}] }}

            - "technicalRequirements" is an ARRAY of AT LEAST 10 items that will hold the list of requirements for this RFI or RFP.
            - "requirement" is an Action verb + Deliverable + Metric for a single requirement
            - "weight" is a percentage of how important the requirement is. Use data in <context> to prioritize
            - "explanation" is a Detailed explanation in 3 sentences, on why the requirement is necessary, AND reference where this 
            requirement was stated

            Important:
            ONLY return A COMPLIANT JSON, DO NOT return any other thing.
        '''.format(context_str=context_str)
        n_llm_attempts = 3
        content = None
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
            ]
        
        for attempt in range(n_llm_attempts):
            try:
                response = await self.llm.ainvoke(messages)
                content = response.content if hasattr(response, "content") else str(response)
                break
            except Exception as e:
                if attempt == n_llm_attempts - 1:
                    raise RuntimeError(f"LLM failed after {n_llm_attempts} attempts: {e}")
                await asyncio.sleep(1)
        return {"content": content, "context": requirements_context} 