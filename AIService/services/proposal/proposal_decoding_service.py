import base64
import os
import secrets
from datetime import datetime
from typing import Optional

from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa 
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.asymmetric import padding as asym_padding

from loguru import logger
from services.proposal.proposal_encryption_service import ProposalEncryptionService

class ProposalDecodingService:
    def __init__(self):
        self.encryption_service = ProposalEncryptionService()

    async def get_public_key(self, db, tenant_id: str) -> Optional[rsa.RSAPublicKey]:
        """Retrieve and parse the public key for a tenant."""
        encryption = await self.encryption_service.get_encryption(db, tenant_id)
        if not encryption or encryption.public_key is None:
            logger.error(f"Public key not found for tenant: {tenant_id}")
            return None
        try:
            public_key_pem = encryption.public_key
            if isinstance(public_key_pem, bytes):
                public_key_pem = public_key_pem.decode("utf-8")
            public_key = serialization.load_pem_public_key(public_key_pem.encode("utf-8"))
            return public_key
        except Exception as e:
            logger.error(f"Error loading public key for tenant {tenant_id}: {e}")
            return None

    def generate_passphrase(self) -> str:
        """Generate a random 5-word passphrase."""
        words = [
            "apple", "banana", "cherry", "date", "elder", "fig", "grape", "honey", "ivory", "jasmine", "kiwi", "lemon",
            "mango", "nutmeg", "orange", "pear", "quince", "raspberry", "strawberry", "tangerine", "ugli", "vanilla",
            "watermelon", "xigua", "yam", "zucchini", "almond", "basil", "cinnamon", "dill", "eggplant", "fennel",
            "ginger", "horseradish", "iceberg", "juniper", "kale", "lavender", "marjoram", "nectarine", "olive",
            "papaya", "quinoa", "rosemary", "sage", "thyme", "udon", "vinegar", "walnut", "xylocarp", "yarrow", "zest"
        ]
        return " ".join(secrets.choice(words) for _ in range(5))

    def generate_salt(self) -> bytes:
        """Generate a random 16-byte salt."""
        return os.urandom(16)

    def hash_passphrase(self, passphrase: str, salt: bytes) -> str:
        """Hash the passphrase with PBKDF2."""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=65536,
        )
        key = kdf.derive(passphrase.encode("utf-8"))
        return base64.b64encode(key).decode("utf-8")

    def generate_key_pair(self):
        """Generate RSA key pair and PEM encode."""
        key = rsa.generate_private_key(public_exponent=65537, key_size=4096)
        private_key_pem = key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        public_key_pem = key.public_key().public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        return key, public_key_pem, private_key_pem

    def encrypt_private_key(self, private_key_pem: bytes, passphrase: str, salt: bytes) -> str:
        """Encrypt the PEM private key with AES using a passphrase-derived key."""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=65536,
        )
        key = kdf.derive(passphrase.encode("utf-8"))
        iv = os.urandom(16)
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
        encryptor = cipher.encryptor()
        # Pad private_key_pem to block size
        pad_len = 16 - (len(private_key_pem) % 16)
        padded = private_key_pem + bytes([pad_len] * pad_len)
        encrypted = encryptor.update(padded) + encryptor.finalize()
        combined = iv + encrypted
        return base64.b64encode(combined).decode("utf-8")

    def decrypt_private_key(self, encrypted_private_key: str, passphrase: str, salt: bytes) -> bytes:
        """Decrypt the PEM private key with AES using a passphrase-derived key."""
        combined = base64.b64decode(encrypted_private_key)
        iv = combined[:16]
        encrypted = combined[16:]
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=65536,
        )
        key = kdf.derive(passphrase.encode("utf-8"))
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
        decryptor = cipher.decryptor()
        padded = decryptor.update(encrypted) + decryptor.finalize()
        pad_len = padded[-1]
        return padded[:-pad_len]

    async def generate_and_save_key_pair(self, db, tenant_id: str):
        """Generate key pair, encrypt private key, and save to DB."""
        key, public_key_pem, private_key_pem = self.generate_key_pair()
        passphrase = self.generate_passphrase()
        salt = self.generate_salt()
        encrypted_private_key = self.encrypt_private_key(private_key_pem, passphrase, salt)
        passphrase_hash = self.hash_passphrase(passphrase, salt)
        await self.encryption_service.save_encryption(
            db,
            tenant_id,
            public_key_pem,
            encrypted_private_key.encode(),
            salt,
            passphrase_hash
        )
        logger.info(f"Saved proposal encryption for tenant {tenant_id}")
        return {
            "public_key": public_key_pem,
            "encrypted_private_key": encrypted_private_key,
            "salt": salt,
            "passphrase_hash": passphrase_hash,
            "passphrase": passphrase
        }

    async def get_key_pair(self, db, tenant_id: str):
        """
        Retrieve the key pair and related data for a tenant from the proposal_encryption table.
        Returns the same structure as generate_and_save_key_pair.
        """
        encryption = await self.encryption_service.get_encryption(db, tenant_id)
        if not encryption:
            logger.error(f"No encryption record found for tenant {tenant_id}")
            return None

        # Ensure all fields are bytes/strings as needed
        public_key_pem = encryption.public_key
        if isinstance(public_key_pem, bytes):
            public_key_pem = public_key_pem.decode("utf-8")

        encrypted_private_key = encryption.encrypted_private_key
        if isinstance(encrypted_private_key, bytes):
            encrypted_private_key = encrypted_private_key.decode("utf-8")

        salt = encryption.salt
        if isinstance(salt, str):
            import base64
            salt = base64.b64decode(salt)
        elif hasattr(salt, '__class__') and 'Column' in str(salt.__class__):
            salt = bytes(salt) if salt is not None else b''

        # The original passphrase is stored in passphrase_hash (despite the name)
        passphrase = encryption.passphrase_hash

        return {
            "public_key": public_key_pem,
            "encrypted_private_key": encrypted_private_key,
            "salt": salt,
            "passphrase_hash": encryption.passphrase_hash,
            "passphrase": passphrase
        }

    async def verify_passphrase(self, db, tenant_id: str, passphrase: str) -> bool:
        """Verify passphrase for a tenant."""
        encryption = await self.encryption_service.get_encryption(db, tenant_id)
        if not encryption or encryption.salt is None or encryption.passphrase_hash is None:
            logger.error(f"Passphrase hash/salt not found for tenant: {tenant_id}")
            return False
        salt = encryption.salt
        if isinstance(salt, str):
            salt = base64.b64decode(salt)
        hashed_input = self.hash_passphrase(passphrase, salt)
        return hashed_input == encryption.passphrase_hash

    async def encrypt_with_public_key(self, db, tenant_id: str, content: bytes) -> bytes:
        """Encrypt content with tenant's public key (hybrid RSA/AES)."""
        public_key = await self.get_public_key(db, tenant_id)
        if not public_key:
            raise RuntimeError("Public key not found")
        # Generate AES key and IV
        aes_key = os.urandom(32)
        iv = os.urandom(16)
        cipher = Cipher(algorithms.AES(aes_key), modes.CBC(iv))
        encryptor = cipher.encryptor()
        pad_len = 16 - (len(content) % 16)
        padded = content + bytes([pad_len] * pad_len)
        encrypted_content = encryptor.update(padded) + encryptor.finalize()
        # Encrypt AES key with RSA
        encrypted_aes_key = public_key.encrypt(
            aes_key,
            asym_padding.OAEP(
                mgf=asym_padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        # Combine IV + encrypted AES key + encrypted content
        return iv + encrypted_aes_key + encrypted_content
    
    
    async def decrypt_section(self, db, tenant_id: str, encrypted_data: bytes, passphrase: str) -> bytes:
        encryption = await self.encryption_service.get_encryption(db, tenant_id)
        if not encryption:
            raise RuntimeError("Encryption data not found")
        salt = encryption.salt
        if isinstance(salt, str):
            salt = base64.b64decode(salt)
        elif isinstance(salt, bytes):
            if len(salt) == 16:
                pass  # raw bytes, use as-is
            elif len(salt) == 24:
                # base64-encoded string as bytes
                salt = base64.b64decode(salt.decode("utf-8"))
            else:
                raise ValueError(f"Unexpected salt length: {len(salt)}")
        else:
            raise TypeError("Salt must be str or bytes")
        encrypted_private_key = encryption.encrypted_private_key
        if not isinstance(encrypted_private_key, str):
            encrypted_private_key = encrypted_private_key.decode("utf-8")

        private_key_pem = self.decrypt_private_key(encrypted_private_key, passphrase, salt)
        # Load private key object
        private_key = serialization.load_pem_private_key(private_key_pem, password=None)
        # Extract IV, encrypted AES key, and encrypted content
        iv = encrypted_data[:16]
        key_size = private_key.key_size // 8  # 4096 bits = 512 bytes
        encrypted_aes_key = encrypted_data[16:16 + key_size]
        encrypted_content = encrypted_data[16 + key_size:]
        # Decrypt AES key
        aes_key = private_key.decrypt(
            encrypted_aes_key,
            asym_padding.OAEP(
                mgf=asym_padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        cipher = Cipher(algorithms.AES(aes_key), modes.CBC(iv))
        decryptor = cipher.decryptor()
        padded = decryptor.update(encrypted_content) + decryptor.finalize()
        pad_len = padded[-1]
        return padded[:-pad_len]

    def encrypt_with_passphrase(self, content: bytes, passphrase: str) -> bytes:
        salt = self.generate_salt()
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=65536,
        )
        key = kdf.derive(passphrase.encode("utf-8"))
        iv = os.urandom(16)
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
        encryptor = cipher.encryptor()
        pad_len = 16 - (len(content) % 16)
        padded = content + bytes([pad_len] * pad_len)
        encrypted_content = encryptor.update(padded) + encryptor.finalize()
        return salt + iv + encrypted_content

    def encrypt_with_passphrase_base64(self, content: bytes, passphrase: str) -> str:
        encrypted = self.encrypt_with_passphrase(content, passphrase)
        return base64.b64encode(encrypted).decode("utf-8")

    def decrypt_with_passphrase(self, encrypted_data: bytes, passphrase: str) -> bytes:
        salt = encrypted_data[:16]
        iv = encrypted_data[16:32]
        encrypted = encrypted_data[32:]
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=65536,
        )
        key = kdf.derive(passphrase.encode("utf-8"))
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
        decryptor = cipher.decryptor()
        padded = decryptor.update(encrypted) + decryptor.finalize()
        pad_len = padded[-1]
        return padded[:-pad_len]

    def decrypt_with_passphrase_from_base64(self, encrypted_base64: str, passphrase: str) -> bytes:
        encrypted_data = base64.b64decode(encrypted_base64)
        return self.decrypt_with_passphrase(encrypted_data, passphrase)

    async def test_encryption_flow(self, db, tenant_id: str):
        # 1. Generate key pair
        key_info = await self.get_key_pair(db, tenant_id)
        if key_info is None:
            return None

        passphrase = key_info["passphrase"]
        salt = key_info["salt"]
        encrypted_private_key = key_info["encrypted_private_key"]
        # 2. Test private key encryption/decryption
        private_key_pem = self.decrypt_private_key(encrypted_private_key, passphrase, salt)
        # 3. Test data encryption/decryption
        test_data = b"Hello, World!"
        encrypted = await self.encrypt_with_public_key(db, tenant_id, test_data)
        logger.info(f"Encrytion done successfully: {encrypted}")
        decrypted = await self.decrypt_section(db, tenant_id, encrypted, passphrase)
        decrypted_str = decrypted.decode("utf-8")
        if decrypted_str != "Hello, World!":
            raise RuntimeError("Encryption/decryption test failed!")
        logger.info("Encryption/decryption test successful!")