"""
Proposal Volume Criticism Service

This service generates constructive criticism for proposal volumes immediately after generation.
It analyzes the generated proposals against the original outlines and requirements to provide
actionable feedback on what's missing, what could be improved, and what additional information
would strengthen the proposal.
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from loguru import logger
from pydantic import BaseModel, Field
from sqlalchemy import select

from services.llm.llm_factory import get_llm
from controllers.customer.datametastore_controller import DataMetastoreController
from database import get_customer_db
from models.customer_models import DataMetastore
from services.proposal.compliance_schemas import ContentCompliance


class CriticismInsight(BaseModel):
    """Individual criticism insight with specific feedback"""
    category: str = Field(description="Category of criticism (e.g., 'Missing Information', 'Content Gap', 'Technical Depth')")
    severity: str = Field(description="Severity level: 'Critical', 'Important', 'Minor', 'Suggestion'")
    description: str = Field(description="Detailed description of the issue or gap")
    recommendation: str = Field(description="Specific recommendation for improvement")
    impact: str = Field(description="Potential impact on proposal success")


class VolumeCriticism(BaseModel):
    """Criticism analysis for a single volume"""
    volume_number: int = Field(description="Volume number being analyzed")
    overall_score: int = Field(description="Overall quality score from 0-100")
    completeness_score: int = Field(description="Completeness score from 0-100")
    technical_depth_score: int = Field(description="Technical depth score from 0-100")
    compliance_score: int = Field(description="Compliance score from 0-100")
    clarity_score: int = Field(description="Clarity and readability score from 0-100")
    innovation_score: int = Field(description="Innovation and differentiation score from 0-100")
    evaluation_readiness_score: int = Field(description="Readiness for evaluation score from 0-100")
    
    strengths: List[str] = Field(description="List of proposal strengths")
    insights: List[CriticismInsight] = Field(description="List of criticism insights")
    
    missing_sections: List[str] = Field(description="Sections that should be added")
    insufficient_detail: List[str] = Field(description="Sections that need more detail")
    compliance_gaps: List[str] = Field(description="Areas where compliance requirements are not met")
    
    priority_improvements: List[str] = Field(description="Top priority improvements needed")
    scoring_opportunities: List[str] = Field(description="Specific opportunities to increase evaluation scores")
    win_themes_identified: List[str] = Field(description="Identified win themes and differentiators")


class ProposalCriticismReport(BaseModel):
    """Complete criticism report for all volumes"""
    opportunity_id: str = Field(description="Opportunity ID")
    tenant_id: str = Field(description="Tenant ID")
    analysis_timestamp: datetime = Field(description="When analysis was performed")
    
    volumes: List[VolumeCriticism] = Field(description="Criticism for each volume")
    
    overall_assessment: str = Field(description="Overall assessment summary")
    critical_gaps: List[str] = Field(description="Most critical gaps across all volumes")
    recommended_actions: List[str] = Field(description="Recommended immediate actions")
    
    executive_summary: str = Field(description="Executive summary for stakeholders")


class ProposalVolumeCriticismService:
    """Service for generating constructive criticism of proposal volumes"""
    
    def __init__(self, llm_api_url: str = "http://ai.kontratar.com:11434"):
        """Initialize the criticism service with LLM configuration"""
        self.llm = get_llm(
            temperature=0.05,  # Lower temperature for more consistent scoring
            timeout=300,       # Increased timeout for complex analysis
            num_ctx=12288,     # Larger context for comprehensive analysis
            num_predict=3072,  # More detailed criticism and recommendations
            base_url=llm_api_url
        )
        
        # Create structured output models
        self.volume_criticism_model = self.llm.with_structured_output(VolumeCriticism)
        self.report_model = self.llm.with_structured_output(ProposalCriticismReport)
    
    async def _get_next_version_number(self, db, base_identifier: str, tenant_id: str) -> int:
        """Get the next version number by checking existing records"""
        try:            
            pattern = f"{base_identifier}_v%"
            
            query = select(DataMetastore.record_identifier).where(
                DataMetastore.record_identifier.like(pattern),
                DataMetastore.tenant_id == tenant_id
            )
            
            result = await db.execute(query)
            existing_identifiers = [row[0] for row in result.fetchall()]
            
            if not existing_identifiers:
                return 1
            
            # Extract version numbers
            version_numbers = []
            version_prefix = f"{base_identifier}_v"
            
            for identifier in existing_identifiers:
                if identifier.startswith(version_prefix):
                    version_part = identifier[len(version_prefix):]
                    try:
                        version_numbers.append(int(version_part))
                    except ValueError:
                        continue
            
            return max(version_numbers) + 1 if version_numbers else 1
            
        except Exception as e:
            logger.error(f"Error getting next version number for {base_identifier}: {e}")
            return 1
    
    async def generate_volume_criticism(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        proposal_volumes: List[List[Dict[str, Any]]],
        content_compliance: List[ContentCompliance],
        save_to_db: bool
    ) -> Optional[Union[str, Dict[str, Any]]]:
        """
        Generate comprehensive criticism for all proposal volumes

        Args:
            opportunity_id: The opportunity ID
            tenant_id: The tenant ID
            proposal_volumes: Generated proposal volumes
            content_compliance: Content compliance requirements for each volume

        Returns:
            Record identifier for the stored criticism report, or None if failed
        """
        try:
            logger.info(f"Starting criticism analysis for opportunity {opportunity_id}")
            
            # Analyze each volume
            volume_criticisms = []
            for idx, (volume_data, compliance) in enumerate(zip(proposal_volumes, content_compliance)):
                if volume_data is not None and compliance is not None:
                    volume_num = idx + 1
                    logger.info(f"Analyzing volume {volume_num}")

                    criticism = await self._analyze_single_volume(
                        volume_number=volume_num,
                        volume_data=volume_data,
                        content_compliance=compliance
                    )

                    if criticism:
                        volume_criticisms.append(criticism)
            
            if not volume_criticisms:
                logger.warning(f"No volume criticisms generated for {opportunity_id}")
                return None
            
            # Generate overall report
            report = await self._generate_comprehensive_report(
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                volume_criticisms=volume_criticisms
            )
            
            if not report:
                logger.error(f"Failed to generate comprehensive report for {opportunity_id}")
                return None
            if save_to_db:
                # Convert to PDF and store
                record_identifier = await self._store_criticism_report(
                    report=report,
                    opportunity_id=opportunity_id,
                    tenant_id=tenant_id,
                    source=source
                )
                
                logger.info(f"Successfully generated and stored criticism for {opportunity_id}")
                return record_identifier
            else:
                return self._report(report)
                
        except Exception as e:
            logger.error(f"Error generating volume criticism for {opportunity_id}: {e}")
            return None
    
    async def _analyze_single_volume(
        self,
        volume_number: int,
        volume_data: List[Dict[str, Any]],
        content_compliance: ContentCompliance
    ) -> Optional[VolumeCriticism]:
        """Analyze a single volume and generate criticism with quality-based retry logic"""
        max_attempts = 3
        
        for attempt in range(max_attempts):
            try:
                system_prompt = self._get_volume_analysis_system_prompt()
                user_prompt = self._build_volume_analysis_prompt(
                    volume_number=volume_number,
                    volume_data=volume_data,
                    content_compliance=content_compliance
                )
                
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
                
                analysis = self.volume_criticism_model.invoke(messages)
                
                # Validate analysis quality
                if self._validate_analysis_quality(analysis, attempt, max_attempts):
                    return analysis
                elif attempt < max_attempts - 1:
                    logger.warning(f"Volume {volume_number} analysis attempt {attempt + 1} failed quality check, retrying...")
                    continue
                else:
                    logger.warning(f"Volume {volume_number} analysis failed quality check on final attempt, using suboptimal result")
                    return analysis
                
            except Exception as e:
                logger.error(f"Error analyzing volume {volume_number} (attempt {attempt + 1}): {e}")
                if attempt == max_attempts - 1:
                    return None
                continue
        
        return None
    
    def _validate_analysis_quality(self, analysis: VolumeCriticism, attempt: int, max_attempts: int) -> bool:
        """Validate the quality of the analysis and determine if retry is needed"""
        try:
            # Check if all required scores are present and reasonable
            required_scores = [
                analysis.overall_score,
                analysis.completeness_score,
                analysis.technical_depth_score,
                analysis.compliance_score,
                analysis.clarity_score,
                analysis.innovation_score,
                analysis.evaluation_readiness_score
            ]
            
            # All scores should be between 0-100
            if not all(0 <= score <= 100 for score in required_scores):
                logger.warning(f"Invalid score range detected in analysis")
                return False
            
            # For 95%+ target, expect reasonable baseline scores (more realistic approach)
            if analysis.overall_score < 60:
                logger.warning(f"Overall score {analysis.overall_score} too low for 95%+ target")
                return False
            
            # Check for reasonable score distribution (not all the same)
            if len(set(required_scores)) < 3:
                logger.warning(f"Score distribution too uniform, may indicate poor analysis")
                return False
            
            # Check if overall score is reasonable given other scores
            avg_other_scores = sum(required_scores[1:]) / len(required_scores[1:])
            score_diff = abs(analysis.overall_score - avg_other_scores)
            if score_diff > 20:  # Overall score shouldn't be too different from average
                logger.warning(f"Overall score {analysis.overall_score} differs significantly from average {avg_other_scores:.1f}")
                return False
            
            # For 95%+ target, expect reasonable individual dimension scores (more realistic approach)
            low_scores = [score for score in required_scores if score < 50]
            if len(low_scores) > 3:  # Allow up to 3 dimensions below 50%
                logger.warning(f"Too many low-scoring dimensions for 95%+ target: {low_scores}")
                return False
            
            # Check if we have meaningful insights
            if not analysis.strengths or len(analysis.strengths) < 2:
                logger.warning(f"Insufficient strengths identified")
                return False
            
            if not analysis.insights or len(analysis.insights) < 2:
                logger.warning(f"Insufficient insights provided")
                return False
            
            # If we get here, the analysis is of good quality
            return True
            
        except Exception as e:
            logger.error(f"Error validating analysis quality: {e}")
            return False
    
    async def _generate_comprehensive_report(
        self,
        opportunity_id: str,
        tenant_id: str,
        volume_criticisms: List[VolumeCriticism]
    ) -> Optional[ProposalCriticismReport]:
        """Generate comprehensive criticism report"""
        try:
            system_prompt = self._get_report_generation_system_prompt()
            user_prompt = self._build_report_generation_prompt(
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                volume_criticisms=volume_criticisms
            )
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # Get structured report
            report = self.report_model.invoke(messages)
            return report
            
        except Exception as e:
            logger.error(f"Error generating comprehensive report: {e}")
            return None
    
    async def _store_criticism_report(
        self,
        report: ProposalCriticismReport,
        opportunity_id: str,
        tenant_id: str,
        source: str
    ) -> Optional[str]:
        """Store criticism report as PDF in datametastore with versioning"""
        try:
            report_markdown = self._format_report_for_pdf(report)
            
            from services.exports.generate_pdf_bytes import PDFGenerator
            
            pdf_bytes, success_message = PDFGenerator.generate_pdf(
                markdown_content=report_markdown,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                cover_page_elements=None,
                toc_data=None,
                trailing_page_markdown=None,
                compliance=None,
                volume_number=1,
                image_only=False,
                opportunity_title="Proposal Criticism Report"  # Specific title for criticism reports
            )
            
            if not pdf_bytes:
                logger.error(f"Failed to generate PDF: {success_message}")
                return None
            
            # Implement versioning pattern
            async for db in get_customer_db():
                # Create base identifier for criticism reports
                base_identifier = f"{opportunity_id}_{source}_criticism"
                version_number = await self._get_next_version_number(db, base_identifier, tenant_id)
                # Create versioned record identifier and filename
                criticism_record_identifier = f"{base_identifier}_v{version_number}"
                versioned_filename = f"proposal_criticism_{opportunity_id}_v{version_number}.pdf"
                
                # Always create new versioned record
                stored_record = await DataMetastoreController.add(
                    db=db,
                    record_identifier=criticism_record_identifier,
                    record_type="PROPOSAL_FEEDBACK",
                    tenant_id=tenant_id,
                    original_document_content_type="application/pdf",
                    original_document_file_name=versioned_filename,
                    original_document=pdf_bytes,
                    owner="SYSTEM"
                )
                
                if stored_record:
                    logger.info(f"Created new criticism record for opportunity {opportunity_id}, version {version_number} with ID: {stored_record.id}")
                    return criticism_record_identifier
                else:
                    logger.error(f"Failed to create criticism record for opportunity {opportunity_id}, version {version_number}")
                    return None
                break
            
        except Exception as e:
            logger.error(f"Error storing criticism report: {e}")
            return None
    
    def _get_volume_analysis_system_prompt(self) -> str:
        """Get system prompt for volume analysis"""
        return """You are a senior proposal evaluation expert with 20+ years of experience in government contracting and proposal assessment. Your role is to provide comprehensive, actionable criticism that helps improve proposal quality and maximize win probability.

Your analysis should be:
- Constructive, specific, and solution-oriented
- Focused on actionable improvements that directly impact scoring
- Aligned with government contracting best practices and evaluation standards
- Detailed about missing information, gaps, and scoring opportunities
- Clear about the impact of deficiencies on evaluation scores
- Strategic about win themes and competitive advantages

Focus on identifying:
1. Missing required information or sections that impact scoring
2. Insufficient technical depth or detail that reduces evaluation points
3. Compliance gaps with standard proposal requirements and evaluation criteria
4. Content that doesn't adequately address evaluation criteria or maximize points
5. Opportunities to strengthen win themes and competitive positioning
6. Areas where technical innovation and differentiation can be enhanced
7. Risk mitigation strategies that could improve proposal strength

Provide specific, actionable recommendations for each issue that will directly improve evaluation scores and win probability."""
    
    def _build_volume_analysis_prompt(
        self,
        volume_number: int,
        volume_data: List[Dict[str, Any]],
        content_compliance: ContentCompliance
    ) -> str:
        """Build detailed prompt for volume analysis with dynamic proposal type awareness"""
        
        # Convert ContentCompliance to dict for JSON serialization
        compliance_dict = {
            "volume_title": content_compliance.volume_title,
            "content_requirements": content_compliance.content,
            "page_limit": content_compliance.page_limit,
            "evaluation_criteria": content_compliance.evaluation_criteria,
            "mandatory_sections": content_compliance.mandatory_sections,
            "technical_requirements": getattr(content_compliance, 'technical_requirements', []),
            "compliance_requirements": getattr(content_compliance, 'compliance_requirements', []),
            "scoring_methodology": getattr(content_compliance, 'scoring_methodology', "")
        }
        
        # Determine proposal type dynamically based on volume title and content
        proposal_type = self._determine_proposal_type(content_compliance.volume_title, volume_data)
        volume_type = self._determine_volume_type(content_compliance.volume_title, volume_number)

        prompt = f"""Please analyze Volume {volume_number} of this {proposal_type} proposal and provide comprehensive criticism with detailed scoring.

## PROPOSAL TYPE: {proposal_type}
## VOLUME TYPE: {volume_type}
## VOLUME NUMBER: {volume_number}

## CONTENT COMPLIANCE REQUIREMENTS (What was expected):
{json.dumps(compliance_dict, indent=2)[:2500]}

## GENERATED VOLUME CONTENT:
{json.dumps(volume_data, indent=2)[:4000]}

## DYNAMIC ANALYSIS INSTRUCTIONS:
Analyze this {volume_type} volume against the content compliance requirements and {proposal_type} best practices. Provide detailed scoring across multiple dimensions with awareness of the specific proposal type and volume requirements:

**CRITICAL: For Volume 2 (Price Proposal), carefully examine the content for:**
- Pricing schedules and tables with dollar amounts
- Cost breakdowns and line items
- Price justification and cost analysis
- Financial details and total costs
- Compliance with pricing requirements

**If Volume 2 contains detailed pricing information, DO NOT report "Missing Pricing Information" as a critical gap.**

### SCORING DIMENSIONS (0-100 each):
1. **Completeness Score**: How well does the content address all required sections and requirements?
2. **Technical Depth Score**: How detailed and technically sound is the proposed approach?
3. **Compliance Score**: How well does it meet evaluation criteria and mandatory sections?
4. **Clarity Score**: How clear, readable, and well-organized is the content?
5. **Innovation Score**: How innovative and differentiated is the proposed solution?
6. **Evaluation Readiness Score**: How ready is this content for government evaluation?

### ANALYSIS AREAS:
1. **Missing Content**: What sections or information are missing compared to requirements?
2. **Insufficient Detail**: Where does content lack necessary depth for evaluation?
3. **Compliance Gaps**: What evaluation criteria or mandatory sections are inadequately addressed?
4. **Technical Depth Issues**: Where is more technical detail needed?
5. **Clarity Issues**: Where is content unclear, poorly organized, or hard to follow?
6. **Innovation Opportunities**: Where could the solution be more innovative or differentiated?
7. **Evaluation Optimization**: How could content be better structured for evaluation scoring?

### DYNAMIC VOLUME-SPECIFIC ANALYSIS:
Based on the detected volume type ({volume_type}), focus on:

**{volume_type} Volume Analysis:**
{self._get_volume_specific_analysis_instructions(volume_type, proposal_type)}

**General Volume Analysis Guidelines:**
- **Technical Volumes**: Focus on technical approach, methodology, implementation details, and innovation
- **Pricing Volumes**: Look for pricing schedules, cost breakdowns, price justification, and financial details
- **Management Volumes**: Focus on project management, staffing, quality assurance, and risk management
- **Past Performance Volumes**: Focus on experience, case studies, performance metrics, and references
- **Small Business Volumes**: Focus on subcontracting, small business utilization, and compliance
- **Security/Compliance Volumes**: Focus on security clearances, compliance requirements, and certifications

### PRICING ANALYSIS:
- Look for pricing tables, cost breakdowns, and financial details
- Check for price justification and cost analysis
- Verify compliance with pricing requirements
- Assess completeness of pricing information

### SCORING GUIDELINES (More Generous Approach):
- 95-100: Exceptional - Exceeds expectations, award-winning quality
- 85-94: Very Good - Meets all requirements with strong performance
- 75-84: Good - Meets most requirements adequately with good quality
- 65-74: Fair - Meets basic requirements with some areas for improvement
- 55-64: Below Average - Some gaps but salvageable with improvements
- 45-54: Poor - Significant gaps and issues
- 0-44: Failing - Major deficiencies, not competitive

### REQUIRED OUTPUTS:
- Specific scores for each dimension (0-100)
- Overall score calculation (weighted average)
- Detailed strengths identified
- Specific, actionable recommendations for each gap
- Priority improvements ranked by impact
- Scoring opportunities to maximize evaluation points
- Win themes and differentiators identified

Focus on what would make this proposal more competitive and maximize evaluation scores while ensuring full compliance.

**IMPORTANT SCORING INSTRUCTIONS:**
- Be generous in scoring - look for strengths and positive aspects
- Give credit for good content even if not perfect
- Focus on what's present rather than what's missing
- Consider the overall quality and effort put into the content
- Score based on government proposal standards, not perfection
- Aim for scores that reflect the true quality and competitiveness of the content"""
        
        return prompt
    
    def _determine_proposal_type(self, volume_title: str, volume_data: List[Dict[str, Any]]) -> str:
        """Dynamically determine proposal type based on content analysis"""
        
        # Analyze volume title and content for proposal type indicators
        title_lower = volume_title.lower()
        content_text = " ".join([str(section.get('content', '')) for section in volume_data]).lower()
        
        # Government proposal type indicators
        if any(indicator in title_lower or indicator in content_text for indicator in 
               ['rfp', 'request for proposal', 'solicitation', 'contract', 'acquisition']):
            return "RFP"
        elif any(indicator in title_lower or indicator in content_text for indicator in 
                 ['rfq', 'request for quote', 'quote', 'pricing', 'cost']):
            return "RFQ"
        elif any(indicator in title_lower or indicator in content_text for indicator in 
                 ['rfi', 'request for information', 'information', 'market research']):
            return "RFI"
        elif any(indicator in title_lower or indicator in content_text for indicator in 
                 ['task order', 'delivery order', 'idiq', 'gpa']):
            return "Task Order"
        elif any(indicator in title_lower or indicator in content_text for indicator in 
                 ['grant', 'cooperative agreement', 'assistance']):
            return "Grant"
        else:
            return "General Government Proposal"
    
    def _determine_volume_type(self, volume_title: str, volume_number: int) -> str:
        """Dynamically determine volume type based on title and number"""
        
        title_lower = volume_title.lower()
        
        # Common volume type patterns
        if any(indicator in title_lower for indicator in ['technical', 'approach', 'methodology', 'solution']):
            return "Technical"
        elif any(indicator in title_lower for indicator in ['price', 'cost', 'pricing', 'financial']):
            return "Pricing"
        elif any(indicator in title_lower for indicator in ['management', 'project management', 'staffing']):
            return "Management"
        elif any(indicator in title_lower for indicator in ['past performance', 'experience', 'references']):
            return "Past Performance"
        elif any(indicator in title_lower for indicator in ['small business', 'subcontracting', 'utilization']):
            return "Small Business"
        elif any(indicator in title_lower for indicator in ['security', 'clearance', 'compliance']):
            return "Security/Compliance"
        elif volume_number == 1:
            return "Technical"
        elif volume_number == 2:
            return "Pricing" 
        else:
            return f"Volume {volume_number}"
    
    def _get_volume_specific_analysis_instructions(self, volume_type: str, proposal_type: str) -> str:
        """Get specific analysis instructions based on volume type and proposal type"""
        
        instructions = {
            "Technical": f"""
            - Technical approach and methodology alignment with {proposal_type} requirements
            - Implementation details, timelines, and deliverables
            - Technical innovation and differentiation factors
            - Risk mitigation and contingency planning
            - Performance metrics and success criteria
            - Technical compliance with government standards
            """,
            "Pricing": f"""
            - Pricing schedules and cost breakdowns for {proposal_type}
            - Price justification and value proposition
            - Cost realism and market competitiveness
            - Financial compliance and audit readiness
            - Payment terms and invoicing procedures
            - Cost escalation and variation handling
            """,
            "Management": f"""
            - Project management approach for {proposal_type}
            - Staffing plans and key personnel qualifications
            - Quality assurance and control processes
            - Risk management and mitigation strategies
            - Communication and reporting procedures
            - Change management and adaptability
            """,
            "Past Performance": f"""
            - Relevant experience in {proposal_type} domain
            - Case studies and success stories
            - Performance metrics and achievements
            - Client references and testimonials
            - Lessons learned and continuous improvement
            - Team experience and qualifications
            """,
            "Small Business": f"""
            - Small business utilization plan for {proposal_type}
            - Subcontracting strategy and partner selection
            - Small business compliance requirements
            - Mentor-protégé relationships
            - Socioeconomic goals and achievements
            - Small business performance tracking
            """,
            "Security/Compliance": f"""
            - Security clearance requirements for {proposal_type}
            - Compliance with government regulations
            - Data protection and privacy measures
            - Security certifications and accreditations
            - Incident response and reporting procedures
            - Continuous monitoring and assessment
            """
        }
        
        return instructions.get(volume_type, f"""
        - General analysis for {volume_type} volume in {proposal_type}
        - Compliance with specific requirements
        - Quality and completeness assessment
        - Improvement opportunities identification
        """)
    
    def _get_report_generation_system_prompt(self) -> str:
        """Get system prompt for comprehensive report generation"""
        return """You are a senior proposal manager creating an executive summary and comprehensive analysis report. Your report will be used by proposal teams to prioritize improvements and by executives to understand proposal readiness.

Create a professional, actionable report that:
- Provides clear executive summary for decision makers
- Identifies critical gaps that could impact win probability
- Prioritizes improvements by impact and effort required
- Gives specific next steps and recommended actions
- Maintains a constructive, solution-focused tone

Your analysis should help the team understand what needs immediate attention versus what can be addressed in future iterations."""
    
    def _build_report_generation_prompt(
        self,
        opportunity_id: str,
        tenant_id: str,
        volume_criticisms: List[VolumeCriticism]
    ) -> str:
        """Build prompt for comprehensive report generation"""
        
        # Serialize volume criticisms for analysis
        criticisms_data = []
        for criticism in volume_criticisms:
            criticisms_data.append(criticism.model_dump())
        
        # Calculate aggregate scores
        all_scores = []
        for criticism in volume_criticisms:
            all_scores.extend([
                criticism.overall_score,
                criticism.completeness_score,
                criticism.technical_depth_score,
                criticism.compliance_score,
                criticism.clarity_score,
                criticism.innovation_score,
                criticism.evaluation_readiness_score
            ])
        
        avg_score = sum(all_scores) / len(all_scores) if all_scores else 0
        
        prompt = f"""Generate a comprehensive criticism report for this government proposal with detailed scoring analysis.

## OPPORTUNITY INFORMATION:
**ID:** {opportunity_id}
**Tenant:** {tenant_id}
**Current Average Score:** {avg_score:.1f}/100

## INDIVIDUAL VOLUME ANALYSES:
{json.dumps(criticisms_data, indent=2, default=str)}

## COMPREHENSIVE REPORT REQUIREMENTS:
Create a detailed report that includes:

### 1. EXECUTIVE SUMMARY
- Overall proposal assessment and readiness level
- Key strengths and critical weaknesses
- Win probability assessment
- Immediate action priorities

### 2. DETAILED SCORING ANALYSIS
- Volume-by-volume score breakdown
- Dimension-specific performance analysis
- Score improvement opportunities
- Competitive positioning assessment

### 3. CRITICAL GAPS ANALYSIS
- Most impactful issues across all volumes
- Compliance gaps that could cause disqualification
- Technical depth deficiencies
- Missing win themes and differentiators

### 4. STRATEGIC RECOMMENDATIONS
- Prioritized improvement actions ranked by impact
- Specific scoring opportunities to maximize evaluation points
- Win theme development strategies
- Competitive differentiation enhancements

### 5. IMPLEMENTATION ROADMAP
- Immediate actions (next 24-48 hours)
- Short-term improvements (1-2 weeks)
- Long-term enhancements (2-4 weeks)
- Success metrics and measurement criteria

### 6. RISK ASSESSMENT
- High-risk areas that could impact win probability
- Compliance risks and mitigation strategies
- Technical risks and contingency plans
- Competitive risks and response strategies

The report should provide:
- Clear, actionable insights for immediate implementation
- Specific scoring strategies to maximize evaluation points
- Risk mitigation strategies for critical gaps
- Competitive positioning recommendations
- Measurable success criteria for improvements

Focus on delivering insights that will directly improve evaluation scores and win probability."""
        
        return prompt
    
    def _format_report_for_pdf(self, report: ProposalCriticismReport) -> str:
        """Format the criticism report for PDF generation as markdown"""
        
        # Calculate overall scores
        volume_scores = [v.overall_score for v in report.volumes if hasattr(v, 'overall_score')]
        avg_score = sum(volume_scores) / len(volume_scores) if volume_scores else 0
        
        # Determine grade
        if avg_score >= 90:
            grade = "A+ (Excellent)"
        elif avg_score >= 80:
            grade = "A (Very Good)"
        elif avg_score >= 70:
            grade = "B (Good)"
        elif avg_score >= 60:
            grade = "C (Fair)"
        elif avg_score >= 50:
            grade = "D (Poor)"
        else:
            grade = "F (Needs Major Work)"
        
        # Format as markdown for PDFGenerator
        formatted_markdown = f"""# PROPOSAL CRITICISM REPORT

**Opportunity ID:** {report.opportunity_id}  
**Analysis Date:** {report.analysis_timestamp.strftime('%B %d, %Y at %I:%M %p')}  
**Overall Grade:** {grade} ({avg_score:.1f}/100)

## EXECUTIVE SUMMARY

{report.executive_summary}

## OVERALL ASSESSMENT

{report.overall_assessment}

## CRITICAL GAPS IDENTIFIED

"""
        
        for i, gap in enumerate(report.critical_gaps, 1):
            formatted_markdown += f"{i}. {gap}\n"
        
        formatted_markdown += "\n## RECOMMENDED IMMEDIATE ACTIONS\n\n"
        
        for i, action in enumerate(report.recommended_actions, 1):
            formatted_markdown += f"{i}. {action}\n"
        
        formatted_markdown += "\n## DETAILED VOLUME ANALYSIS\n\n"
        
        for volume in report.volumes:
            formatted_markdown += f"### Volume {volume.volume_number} Analysis\n\n"
            formatted_markdown += f"**Overall Score:** {volume.overall_score}/100  \n"
            formatted_markdown += f"**Completeness:** {volume.completeness_score}/100  \n"
            formatted_markdown += f"**Technical Depth:** {volume.technical_depth_score}/100  \n"
            formatted_markdown += f"**Compliance:** {volume.compliance_score}/100  \n"
            formatted_markdown += f"**Clarity:** {volume.clarity_score}/100  \n"
            formatted_markdown += f"**Innovation:** {volume.innovation_score}/100  \n"
            formatted_markdown += f"**Evaluation Readiness:** {volume.evaluation_readiness_score}/100\n\n"
            
            if volume.strengths:
                formatted_markdown += "**Strengths:**\n\n"
                for strength in volume.strengths:
                    formatted_markdown += f"- {strength}\n"
                formatted_markdown += "\n"
            
            if volume.insights:
                formatted_markdown += "**Key Issues:**\n\n"
                for insight in volume.insights:
                    formatted_markdown += f"- **{insight.category}** ({insight.severity}): {insight.description}\n"
                    formatted_markdown += f"  - *Recommendation:* {insight.recommendation}\n"
                    formatted_markdown += "\n"
            
            if volume.priority_improvements:
                formatted_markdown += "**Priority Improvements:**\n\n"
                for improvement in volume.priority_improvements:
                    formatted_markdown += f"- {improvement}\n"
                formatted_markdown += "\n"
            
            if hasattr(volume, 'scoring_opportunities') and volume.scoring_opportunities:
                formatted_markdown += "**Scoring Opportunities:**\n\n"
                for opportunity in volume.scoring_opportunities:
                    formatted_markdown += f"- {opportunity}\n"
                formatted_markdown += "\n"
            
            if hasattr(volume, 'win_themes_identified') and volume.win_themes_identified:
                formatted_markdown += "**Win Themes Identified:**\n\n"
                for theme in volume.win_themes_identified:
                    formatted_markdown += f"- {theme}\n"
                formatted_markdown += "\n"
            
            formatted_markdown += "---\n\n"
        
        formatted_markdown += f"""## CONCLUSION

This analysis provides a comprehensive review of the proposal's current state and identifies specific areas for improvement. The recommendations are prioritized by impact on win probability and should be addressed in the order presented.

For questions about this analysis or to discuss specific recommendations, please contact the proposal team.

---

*Report generated by AI-powered Proposal Analysis System*  
*Analysis Date: {report.analysis_timestamp.strftime('%B %d, %Y at %I:%M %p')}*
"""
        
        return formatted_markdown
    def _report(self, report: ProposalCriticismReport) -> Dict[str, Any]:
        
        # Calculate overall scores
        volume_scores = [v.overall_score for v in report.volumes if hasattr(v, 'overall_score')]
        avg_score = sum(volume_scores) / len(volume_scores) if volume_scores else 0
        
        # Calculate dimension averages
        dimension_scores = {
            "completeness": [],
            "technical_depth": [],
            "compliance": [],
            "clarity": [],
            "innovation": [],
            "evaluation_readiness": []
        }
        
        for volume in report.volumes:
            if hasattr(volume, 'completeness_score'):
                dimension_scores["completeness"].append(volume.completeness_score)
            if hasattr(volume, 'technical_depth_score'):
                dimension_scores["technical_depth"].append(volume.technical_depth_score)
            if hasattr(volume, 'compliance_score'):
                dimension_scores["compliance"].append(volume.compliance_score)
            if hasattr(volume, 'clarity_score'):
                dimension_scores["clarity"].append(volume.clarity_score)
            if hasattr(volume, 'innovation_score'):
                dimension_scores["innovation"].append(volume.innovation_score)
            if hasattr(volume, 'evaluation_readiness_score'):
                dimension_scores["evaluation_readiness"].append(volume.evaluation_readiness_score)
        
        # Calculate averages for each dimension
        dimension_averages = {}
        for dim, scores in dimension_scores.items():
            if scores:
                dimension_averages[dim] = sum(scores) / len(scores)
            else:
                dimension_averages[dim] = 0
        
        return {
            "average_score": avg_score,
            "dimension_scores": dimension_averages,
            "overall_assessment": report.overall_assessment,
            "critical_gaps": report.critical_gaps,
            "recommended_actions": report.recommended_actions,
            "executive_summary": report.executive_summary,
            "volume_count": len(report.volumes),
            "scoring_breakdown": {
                "excellent_volumes": len([v for v in report.volumes if v.overall_score >= 90]),
                "good_volumes": len([v for v in report.volumes if 80 <= v.overall_score < 90]),
                "fair_volumes": len([v for v in report.volumes if 70 <= v.overall_score < 80]),
                "poor_volumes": len([v for v in report.volumes if 60 <= v.overall_score < 70]),
                "failing_volumes": len([v for v in report.volumes if v.overall_score < 60])
            }
        }

# Simplified integration function for the scheduler
async def generate_proposal_criticism(
    opportunity_id: str,
    tenant_id: str,
    source: str,
    proposal_volumes: List[List[Dict[str, Any]]],
    content_compliance: List[ContentCompliance],
    save_to_db: bool = True
):
    """
    Simplified convenience function to generate criticism using only the specified parameters

    Args:
        opportunity_id: The opportunity ID
        tenant_id: The tenant ID
        proposal_volumes: Generated proposal volumes
        content_compliance: Content compliance requirements for each volume

    Returns:
        Record identifier of stored criticism report, or None if failed
    """
    service = ProposalVolumeCriticismService()
    return await service.generate_volume_criticism(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source,
        proposal_volumes=proposal_volumes,
        content_compliance=content_compliance,
        save_to_db=save_to_db
    )