"""
Document Title Extraction Service

This service uses LLM to intelligently extract document titles and submission requirements
from solicitation data, providing meaningful names for generated proposal documents.
"""

import json
import re
from typing import Dict, List, Optional, Any
from loguru import logger

from services.llm.llm_factory import get_llm
from services.chroma.chroma_service import ChromaService
from database import get_kontratar_db, get_customer_db
from config import settings


class DocumentTitleExtractionService:
    """Service for extracting intelligent document titles from solicitation data"""

    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.max_tokens = 3096
        self.chroma_service = ChromaService(embedding_api_url, None)
        # Use the LLM factory to get the configured LLM
        self.llm = get_llm(
            temperature=0,
            # Pass additional parameters for Ollama if needed
            num_ctx=6000,
            num_predict=self.max_tokens,
            base_url=llm_api_url  # For backward compatibility with Ollama
        )
        
    async def extract_document_title(
        self,
        opportunity_data: Dict[str, Any],
        volume_number: int,
        volume_title: str,
        source: str = "custom"
    ) -> str:
        """
        Extract intelligent document title from opportunity data using LLM
        
        Args:
            opportunity_data: Dictionary containing opportunity information
            volume_number: Volume number (1-5)
            volume_title: Current volume title from TOC
            source: Source type (custom, sam, ebuy)
            
        Returns:
            Intelligent document title string
        """
        try:
            # Prepare opportunity context for LLM
            context = self._prepare_opportunity_context(opportunity_data, source)
            
            # Create prompt for title extraction
            prompt = self._create_title_extraction_prompt(
                context, volume_number, volume_title
            )
            
            # Get LLM response
            messages = [
                {"role": "system", "content": "You are an expert in government contracting and proposal writing."},
                {"role": "user", "content": prompt}
            ]
            response = await self.llm.invoke(messages)
            
            # Extract and clean the title
            extracted_title = self._clean_extracted_title(response.content, volume_title)
            
            logger.info(f"Extracted document title for volume {volume_number}: {extracted_title}")
            return extracted_title
            
        except Exception as e:
            logger.error(f"Error extracting document title: {e}")
            # Fallback to volume title
            return self._create_fallback_title(volume_title, volume_number)
    
    def _prepare_opportunity_context(
        self, 
        opportunity_data: Dict[str, Any], 
        source: str
    ) -> str:
        """Prepare opportunity context for LLM analysis"""
        try:
            context_parts = []
            
            # Add title and description
            if hasattr(opportunity_data, 'title') and opportunity_data.title:
                context_parts.append(f"Opportunity Title: {opportunity_data.title}")
            
            if hasattr(opportunity_data, 'description') and opportunity_data.description:
                # Truncate description if too long
                description = str(opportunity_data.description)[:1000]
                context_parts.append(f"Description: {description}")
            
            # Add solicitation number if available
            if hasattr(opportunity_data, 'solicitation_number') and opportunity_data.solicitation_number:
                context_parts.append(f"Solicitation Number: {opportunity_data.solicitation_number}")
            
            # Add agency information
            if hasattr(opportunity_data, 'full_parent_path_name') and opportunity_data.full_parent_path_name:
                context_parts.append(f"Agency: {opportunity_data.full_parent_path_name}")
            
            # Add NAICS code
            if hasattr(opportunity_data, 'naics_code') and opportunity_data.naics_code:
                context_parts.append(f"NAICS Code: {opportunity_data.naics_code}")
            
            # Add opportunity type
            if hasattr(opportunity_data, 'opportunity_type') and opportunity_data.opportunity_type:
                context_parts.append(f"Type: {opportunity_data.opportunity_type}")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error preparing opportunity context: {e}")
            return "Limited opportunity information available"
    
    def _create_title_extraction_prompt(
        self, 
        context: str, 
        volume_number: int, 
        volume_title: str
    ) -> str:
        """Create prompt for LLM title extraction"""
        return f"""
You are an expert in government contracting and proposal writing. Based on the following solicitation information, extract an intelligent and professional document title for Volume {volume_number} ({volume_title}) of a proposal response.

Solicitation Information:
{context}

Current Volume: Volume {volume_number} - {volume_title}

Instructions:
1. Analyze the solicitation to understand what type of document submission is required
2. Look for specific document naming requirements or submission instructions
3. Consider the volume type and typical government proposal structure
4. Create a professional, descriptive title that would be appropriate for this volume
5. The title should be concise (under 60 characters) and professional
6. If specific naming requirements are mentioned in the solicitation, follow them
7. If no specific requirements, use standard government proposal naming conventions

Common volume types and their typical titles:
- Volume I: Technical Capability, Technical Approach, Technical Proposal
- Volume II: Price Proposal, Cost Proposal, Pricing
- Volume III: Management Approach, Management Plan
- Volume IV: Past Performance, Corporate Experience
- Volume V: Additional Requirements, Appendices

Respond with ONLY the document title, nothing else. Do not include "Volume" or volume numbers in the title unless specifically required by the solicitation.

Document Title:"""
    
    def _clean_extracted_title(self, llm_response: str, fallback_title: str) -> str:
        """Clean and validate the extracted title"""
        try:
            # Extract the title from LLM response
            title = llm_response.strip()
            
            # Remove common prefixes/suffixes
            title = re.sub(r'^(Document Title:|Title:|Response:)\s*', '', title, flags=re.IGNORECASE)
            title = re.sub(r'\s*(Document|Title)\s*$', '', title, flags=re.IGNORECASE)
            
            # Remove quotes if present
            title = title.strip('"\'')
            
            # Validate length
            if len(title) > 80:
                title = title[:77] + "..."
            
            # Validate content
            if len(title) < 3 or not title.replace(' ', '').replace('-', '').replace('_', '').isalnum():
                return self._create_fallback_title(fallback_title, 1)
            
            # Clean for filename use
            title = re.sub(r'[<>:"/\\|?*]', '', title)
            title = re.sub(r'\s+', ' ', title).strip()
            
            return title
            
        except Exception as e:
            logger.error(f"Error cleaning extracted title: {e}")
            return self._create_fallback_title(fallback_title, 1)
    
    def _create_fallback_title(self, volume_title: str, volume_number: int) -> str:
        """Create fallback title when extraction fails"""
        # Clean volume title
        clean_title = volume_title.replace('_', ' ').replace('-', ' ')
        clean_title = re.sub(r'\s+', ' ', clean_title).strip()
        
        # Remove "Volume" prefix if present
        clean_title = re.sub(r'^Volume\s+\w+\s*[-:]?\s*', '', clean_title, flags=re.IGNORECASE)
        
        if clean_title and len(clean_title) > 3:
            return clean_title
        else:
            # Ultimate fallback
            volume_names = {
                1: "Technical Proposal",
                2: "Price Proposal", 
                3: "Management Approach",
                4: "Past Performance",
                5: "Additional Requirements"
            }
            return volume_names.get(volume_number, f"Proposal Volume {volume_number}")
    
    async def extract_submission_requirements(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str = "custom"
    ) -> Dict[str, Any]:
        """
        Extract submission requirements and document specifications from solicitation using ChromaDB
        """
        try:
            logger.info(f"Starting extract_submission_requirements for opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")

            # ChromaDB query to find submission requirements
            chroma_query = '''
                Find document submission requirements including:
                - Document format requirements (PDF, DOCX, etc.)
                - File naming conventions and requirements
                - Page limits or formatting requirements
                - Specific document titles required
                - Submission deadlines and due dates
                - Email submission details and addresses
                - Alternative submission methods
                - Special instructions for proposal format
                - Contact information for submissions
                - Required email subject lines or templates
            '''

            # Get context from ChromaDB
            requirements_context = []
            context_str = ""

            # Determine which database to use based on source
            if source.lower() == "custom":
                async for db in get_customer_db():
                    max_chunks = 8
                    collection_name = f"{tenant_id}_{opportunity_id}"
                    logger.debug(f"Querying ChromaDB with collection_name={collection_name}, chroma_query={chroma_query.strip()}, max_chunks={max_chunks}")
                    relevant_chunks = await self.chroma_service.get_relevant_chunks(
                        db, collection_name, chroma_query, n_results=max_chunks
                    )
                    requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
                    context_str = "\n\n".join(requirements_context)
                    logger.debug(f"Retrieved {len(requirements_context)} relevant context chunks for submission requirements.")
                    break
            else:
                async for db in get_kontratar_db():
                    max_chunks = 8
                    collection_name = opportunity_id
                    logger.debug(f"Querying ChromaDB with collection_name={collection_name}, chroma_query={chroma_query.strip()}, max_chunks={max_chunks}")
                    relevant_chunks = await self.chroma_service.get_relevant_chunks(
                        db, collection_name, chroma_query, n_results=max_chunks
                    )
                    requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
                    context_str = "\n\n".join(requirements_context)
                    logger.debug(f"Retrieved {len(requirements_context)} relevant context chunks for submission requirements.")
                    break

            if not context_str:
                logger.warning(f"No context found for submission requirements extraction for opportunity {opportunity_id}")
                return self._get_default_requirements()
            
            # Create prompt for LLM analysis
            prompt = f"""
You are an expert in government contracting. Analyze the following solicitation information and extract document submission requirements with special focus on email submission details.

Solicitation Context:
{context_str}

Extract the following information if mentioned:

1. Document format requirements (PDF, DOCX, etc.)
2. File naming conventions
3. Page limits or formatting requirements
4. Specific document titles required (be very detailed - extract ALL mentioned document names/titles)
5. Submission deadlines
6. Email submission details (if applicable):
   - Email address to submit to
   - Required email subject line format
   - Email body requirements or template
   - Any CC or BCC requirements
7. Alternative submission methods (portal, physical delivery, etc.)
8. Any special instructions for proposal format

Respond in JSON format with the following structure:
{{
    "format_requirements": "string describing format requirements",
    "naming_convention": "string describing naming requirements", 
    "page_limits": "string describing page limits",
    "document_titles": ["list", "of", "all", "required", "document", "titles", "and", "names"],
    "submission_deadlines": "string with deadline information",
    "email_submission": {{
        "required": true/false,
        "email_address": "submission email if specified",
        "subject_format": "required subject line format or template",
        "body_template": "required email body content or format",
        "cc_addresses": ["list", "of", "cc", "emails"],
        "bcc_addresses": ["list", "of", "bcc", "emails"],
        "special_email_instructions": "any special email requirements"
    }},
    "alternative_submission_methods": ["list", "of", "other", "submission", "methods"],
    "special_instructions": "string with any special formatting instructions"
}}

If no specific requirements are found for a field, use null. For boolean fields, use false if not applicable.

JSON Response:"""
            
            messages = [
                {"role": "system", "content": "You are an expert in government contracting with expertise in submission requirements analysis."},
                {"role": "user", "content": prompt}
            ]
            response = await self.llm.ainvoke(messages)

            # Parse JSON response
            try:
                # Extract JSON from response content (handle markdown code blocks)
                response_content = response.content.strip()

                # Check if response is wrapped in markdown code blocks
                if response_content.startswith('```json') and response_content.endswith('```'):
                    # Extract JSON content between code blocks
                    json_start = response_content.find('```json') + 7
                    json_end = response_content.rfind('```')
                    json_content = response_content[json_start:json_end].strip()
                elif response_content.startswith('```') and response_content.endswith('```'):
                    # Handle generic code blocks
                    json_start = response_content.find('```') + 3
                    json_end = response_content.rfind('```')
                    json_content = response_content[json_start:json_end].strip()
                else:
                    # No code blocks, use content as-is
                    json_content = response_content

                # Parse the extracted JSON
                requirements = json.loads(json_content)

                # Post-process to ensure email submission structure is complete
                requirements = self._enhance_email_submission_data(requirements, {})

                logger.info("Successfully extracted submission requirements using ChromaDB context")
                return requirements
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse submission requirements JSON: {e}")
                logger.debug(f"Raw response content: {response.content}")
                return self._get_default_requirements()
                
        except Exception as e:
            logger.error(f"Error extracting submission requirements: {e}")
            return self._get_default_requirements()
    
    def _enhance_email_submission_data(self, requirements: Dict[str, Any], opportunity_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance email submission data with constructed email details
        """
        try:
            email_info = requirements.get("email_submission", {})
            
            if email_info and email_info.get("required", False):
                # Construct email subject if not provided but format is given
                if not email_info.get("subject_format") and opportunity_data:
                    solicitation_number = opportunity_data.get("solicitation_number", "")
                    title = opportunity_data.get("title", "")
                    
                    if solicitation_number:
                        email_info["subject_format"] = f"Proposal Submission - {solicitation_number}"
                    elif title:
                        email_info["subject_format"] = f"Proposal Submission - {title}"
                
                # Construct basic email body template if not provided
                if not email_info.get("body_template"):
                    email_info["body_template"] = self._generate_default_email_body(opportunity_data)
                
                # Ensure all email fields exist
                email_defaults = {
                    "required": False,
                    "email_address": None,
                    "subject_format": None,
                    "body_template": None,
                    "cc_addresses": [],
                    "bcc_addresses": [],
                    "special_email_instructions": None
                }
                
                for key, default_value in email_defaults.items():
                    if key not in email_info:
                        email_info[key] = default_value
                
                requirements["email_submission"] = email_info
            
            return requirements
            
        except Exception as e:
            logger.error(f"Error enhancing email submission data: {e}")
            return requirements
    
    def _generate_default_email_body(self, opportunity_data: Dict[str, Any]) -> str:
        """
        Generate a default professional email body template
        """
        solicitation_number = opportunity_data.get("solicitation_number", "[SOLICITATION_NUMBER]")
        title = opportunity_data.get("title", "[SOLICITATION_TITLE]")
        
        return f"""Dear Contracting Officer,

Please find attached our proposal submission for:

Solicitation Number: {solicitation_number}
Title: {title}

The submission includes all required documents as specified in the solicitation.

Please confirm receipt of this submission.

Thank you for your consideration.

Best regards,
[YOUR_NAME]
[YOUR_COMPANY]
[CONTACT_INFORMATION]"""
    
    def _get_default_requirements(self) -> Dict[str, Any]:
        """Get default submission requirements with enhanced structure"""
        return {
            "format_requirements": None,
            "naming_convention": None,
            "page_limits": None,
            "document_titles": [],
            "submission_deadlines": None,
            "email_submission": {
                "required": False,
                "email_address": None,
                "subject_format": None,
                "body_template": None,
                "cc_addresses": [],
                "bcc_addresses": [],
                "special_email_instructions": None
            },
            "alternative_submission_methods": [],
            "special_instructions": None
        }
    
    def get_email_submission_details(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract and format email submission details for easy use
        
        Args:
            requirements: The result from extract_submission_requirements
            
        Returns:
            Dictionary with ready-to-use email details
        """
        email_info = requirements.get("email_submission", {})
        
        if not email_info.get("required", False):
            return {"email_required": False}
        
        return {
            "email_required": True,
            "to": email_info.get("email_address"),
            "subject": email_info.get("subject_format"),
            "body": email_info.get("body_template"),
            "cc": email_info.get("cc_addresses", []),
            "bcc": email_info.get("bcc_addresses", []),
            "special_instructions": email_info.get("special_email_instructions"),
            "document_titles": requirements.get("document_titles", []),
            "format_requirements": requirements.get("format_requirements"),
            "naming_convention": requirements.get("naming_convention")
        }
    