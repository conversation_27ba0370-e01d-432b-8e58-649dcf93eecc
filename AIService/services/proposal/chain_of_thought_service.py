"""
Chain of Thought Service for Proposal Generation

This service implements step-by-step reasoning for complex proposal generation tasks,
particularly for the draft generation process where sophisticated analysis and 
decision-making is required.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Tuple
from loguru import logger
from services.llm.llm_factory import get_llm


class ChainOfThoughtService:
    """
    Service that implements chain of thought reasoning for proposal generation.
    
    This service breaks down complex proposal generation tasks into step-by-step
    reasoning processes, improving the quality and compliance of generated content.
    """
    
    def __init__(self, llm_api_url: str = "http://ai.kontratar.com:11434"):
        """Initialize the chain of thought service with LLM configuration."""
        self.llm = get_llm(
            temperature=0.1,  # Low temperature for consistent reasoning
            num_ctx=8192,     # Large context for comprehensive analysis
            num_predict=4096, # Sufficient tokens for detailed reasoning
            base_url=llm_api_url
        )
    
    async def generate_section_with_chain_of_thought(
        self,
        section_title: str,
        section_description: str,
        section_number: str,
        page_limit: int,
        rfp_context: str,
        client_context: str,
        tenant_metadata: str,
        compliance_requirements: Dict[str, Any],
        previous_sections_summary: str = "",
        is_cover_letter: bool = False,
        is_personnel_section: bool = False,
        personnel_context: str = ""
    ) -> Dict[str, Any]:
        """
        Generate section content using chain of thought reasoning.
        
        This method implements a multi-step reasoning process:
        1. Requirement Analysis
        2. Context Integration  
        3. Compliance Verification
        4. Content Strategy Planning
        5. Quality Assurance Planning
        6. Content Generation
        7. Final Validation
        
        Returns:
            Dict containing generated content, reasoning steps, and metadata
        """
        
        logger.info(f"COT: Starting chain of thought generation for '{section_title}'")
        
        try:
            # Step 1: Analyze requirements and plan approach
            reasoning_result = await self._execute_reasoning_chain(
                section_title=section_title,
                section_description=section_description,
                page_limit=page_limit,
                rfp_context=rfp_context,
                client_context=client_context,
                compliance_requirements=compliance_requirements,
                previous_sections_summary=previous_sections_summary,
                is_cover_letter=is_cover_letter,
                is_personnel_section=is_personnel_section,
                personnel_context=personnel_context
            )
            
            if not reasoning_result.get('success', False):
                raise Exception(f"Reasoning chain failed: {reasoning_result.get('error', 'Unknown error')}")
            
            # Step 2: Generate content based on reasoning
            content_result = await self._generate_content_from_reasoning(
                section_title=section_title,
                section_number=section_number,
                reasoning_analysis=reasoning_result['analysis'],
                content_strategy=reasoning_result['strategy'],
                rfp_context=rfp_context,
                client_context=client_context,
                tenant_metadata=tenant_metadata,
                personnel_context=personnel_context,
                is_cover_letter=is_cover_letter
            )
            
            if not content_result.get('success', False):
                raise Exception(f"Content generation failed: {content_result.get('error', 'Unknown error')}")
            
            # Step 3: Validate and refine if needed
            validation_result = await self._validate_generated_content(
                content=content_result['content'],
                reasoning_analysis=reasoning_result['analysis'],
                compliance_requirements=compliance_requirements,
                page_limit=page_limit
            )
            
            logger.info(f"COT: Successfully generated section '{section_title}' with chain of thought")
            
            return {
                'success': True,
                'content': content_result['content'],
                'reasoning_steps': reasoning_result['reasoning_steps'],
                'content_strategy': reasoning_result['strategy'],
                'validation_results': validation_result,
                'metadata': {
                    'section_title': section_title,
                    'reasoning_quality': reasoning_result.get('quality_score', 0),
                    'content_length': len(content_result['content']),
                    'word_count': len(content_result['content'].split()),
                    'validation_passed': validation_result.get('passed', False),
                    'chain_of_thought_applied': True
                }
            }
            
        except Exception as e:
            logger.error(f"COT: Chain of thought generation failed for '{section_title}': {e}")
            return {
                'success': False,
                'error': str(e),
                'content': '',
                'reasoning_steps': [],
                'metadata': {
                    'section_title': section_title,
                    'chain_of_thought_applied': False,
                    'error_occurred': True
                }
            }
    
    async def _execute_reasoning_chain(
        self,
        section_title: str,
        section_description: str,
        page_limit: int,
        rfp_context: str,
        client_context: str,
        compliance_requirements: Dict[str, Any],
        previous_sections_summary: str,
        is_cover_letter: bool,
        is_personnel_section: bool,
        personnel_context: str
    ) -> Dict[str, Any]:
        """Execute the step-by-step reasoning chain for section analysis."""
        
        reasoning_prompt = self._build_reasoning_prompt(
            section_title, section_description, page_limit, rfp_context,
            client_context, compliance_requirements, previous_sections_summary,
            is_cover_letter, is_personnel_section, personnel_context
        )
        
        try:
            messages = [
                ("system", self._get_reasoning_system_prompt()),
                ("human", reasoning_prompt)
            ]
            
            result = await asyncio.wait_for(
                asyncio.to_thread(self.llm.invoke, messages),
                timeout=180.0  # 3 minute timeout for reasoning
            )
            
            reasoning_content = str(result.content).strip()
            
            # Parse the structured reasoning response
            parsed_reasoning = self._parse_reasoning_response(reasoning_content)
            
            return {
                'success': True,
                'analysis': parsed_reasoning.get('analysis', {}),
                'strategy': parsed_reasoning.get('strategy', {}),
                'reasoning_steps': parsed_reasoning.get('steps', []),
                'quality_score': parsed_reasoning.get('quality_score', 0),
                'raw_reasoning': reasoning_content
            }
            
        except Exception as e:
            logger.error(f"COT: Reasoning chain execution failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _get_reasoning_system_prompt(self) -> str:
        """Get the system prompt for the reasoning chain."""
        return '''
**ROLE:** Senior Government Proposal Strategy Analyst with Chain of Thought Reasoning

**MISSION:** Analyze proposal section requirements using systematic step-by-step reasoning to develop optimal content generation strategies that maximize compliance and evaluation scores.

**REASONING METHODOLOGY:**
You must think through each section systematically using the following chain of thought process:

1. **REQUIREMENT ANALYSIS** - What exactly is being asked?
2. **CONTEXT INTEGRATION** - What information do I have to work with?
3. **COMPLIANCE VERIFICATION** - What are the mandatory requirements?
4. **CONTENT STRATEGY** - What's the optimal approach?
5. **QUALITY ASSURANCE** - How do I ensure excellence?

**OUTPUT FORMAT:**
Provide your analysis in structured JSON format with the following sections:
- analysis: Detailed breakdown of requirements and context
- strategy: Content generation strategy and approach
- steps: Array of reasoning steps taken
- quality_score: Predicted quality score (0-100)
- recommendations: Specific recommendations for content generation

**CRITICAL REQUIREMENTS:**
- Use systematic step-by-step reasoning for every decision
- Consider government evaluation criteria and scoring methodology
- Ensure all compliance requirements are identified and addressed
- Develop specific, actionable content generation strategies
- Provide clear reasoning for all recommendations
'''
    
    def _build_reasoning_prompt(
        self,
        section_title: str,
        section_description: str,
        page_limit: int,
        rfp_context: str,
        client_context: str,
        compliance_requirements: Dict[str, Any],
        previous_sections_summary: str,
        is_cover_letter: bool,
        is_personnel_section: bool,
        personnel_context: str
    ) -> str:
        """Build the comprehensive reasoning prompt."""
        
        return f'''
**SECTION TO ANALYZE:**
Title: {section_title}
Description: {section_description}
Page Limit: {page_limit} pages
Type: {"Cover Letter" if is_cover_letter else "Personnel Section" if is_personnel_section else "Technical Section"}

**RFP CONTEXT:**
{rfp_context[:2000] if rfp_context else "No RFP context available"}

**CLIENT CAPABILITIES:**
{client_context[:1000] if client_context else "No client context available"}

**PERSONNEL INFORMATION:**
{personnel_context[:1000] if personnel_context and is_personnel_section else "Not applicable"}

**COMPLIANCE REQUIREMENTS:**
{json.dumps(compliance_requirements, indent=2) if compliance_requirements else "No specific compliance requirements"}

**PREVIOUS SECTIONS SUMMARY:**
{previous_sections_summary[:1500] if previous_sections_summary else "No previous sections"}

**REASONING TASK:**
Using systematic chain of thought reasoning, analyze this section and develop an optimal content generation strategy.

**STEP-BY-STEP ANALYSIS REQUIRED:**

1. **REQUIREMENT ANALYSIS:**
   - What are the specific requirements for this section?
   - What evaluation criteria will be used?
   - What are the key deliverables expected?
   - What compliance standards must be met?

2. **CONTEXT INTEGRATION:**
   - What relevant RFP requirements apply to this section?
   - What client capabilities should be highlighted?
   - How does this section relate to previous sections?
   - What specific evidence or examples are available?

3. **COMPLIANCE VERIFICATION:**
   - What are the mandatory compliance requirements?
   - What formatting or structural requirements exist?
   - What content must be included or excluded?
   - What government standards must be addressed?

4. **CONTENT STRATEGY:**
   - What's the optimal narrative structure for this section?
   - What key messages should be emphasized?
   - What evidence and examples should be included?
   - How should the content be organized for maximum impact?

5. **QUALITY ASSURANCE:**
   - What quality standards must be met?
   - What potential issues should be avoided?
   - How can the content be optimized for evaluation?
   - What validation checks should be performed?

Provide your complete analysis in structured JSON format.
'''

    def _parse_reasoning_response(self, reasoning_content: str) -> Dict[str, Any]:
        """Parse the structured reasoning response from the LLM."""
        try:
            # Clean the content - remove markdown code blocks if present
            cleaned_content = reasoning_content.strip()
            if cleaned_content.startswith("```json"):
                cleaned_content = cleaned_content.replace("```json", "").replace("```", "").strip()
            elif cleaned_content.startswith("```"):
                cleaned_content = cleaned_content.replace("```", "").strip()

            # Try to parse as JSON
            parsed = json.loads(cleaned_content)

            # Validate required fields
            required_fields = ['analysis', 'strategy', 'steps', 'quality_score']
            for field in required_fields:
                if field not in parsed:
                    logger.warning(f"COT: Missing required field '{field}' in reasoning response")
                    parsed[field] = {} if field in ['analysis', 'strategy'] else [] if field == 'steps' else 0

            return parsed

        except json.JSONDecodeError as e:
            logger.error(f"COT: Failed to parse reasoning response as JSON: {e}")
            # Return fallback structure
            return {
                'analysis': {'error': 'Failed to parse reasoning response'},
                'strategy': {'approach': 'fallback'},
                'steps': ['Reasoning parsing failed'],
                'quality_score': 50,
                'raw_content': reasoning_content
            }

    async def _generate_content_from_reasoning(
        self,
        section_title: str,
        section_number: str,
        reasoning_analysis: Dict[str, Any],
        content_strategy: Dict[str, Any],
        rfp_context: str,
        client_context: str,
        tenant_metadata: str,
        personnel_context: str,
        is_cover_letter: bool
    ) -> Dict[str, Any]:
        """Generate content based on the reasoning analysis and strategy."""

        content_prompt = self._build_content_generation_prompt(
            section_title, section_number, reasoning_analysis, content_strategy,
            rfp_context, client_context, tenant_metadata, personnel_context, is_cover_letter
        )

        try:
            messages = [
                ("system", self._get_content_generation_system_prompt()),
                ("human", content_prompt)
            ]

            result = await asyncio.wait_for(
                asyncio.to_thread(self.llm.invoke, messages),
                timeout=240.0  # 4 minute timeout for content generation
            )

            content = str(result.content).strip()

            if not content or len(content) < 100:
                raise Exception("Generated content is too short or empty")

            return {
                'success': True,
                'content': content,
                'content_length': len(content),
                'word_count': len(content.split())
            }

        except Exception as e:
            logger.error(f"COT: Content generation failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'content': ''
            }

    def _get_content_generation_system_prompt(self) -> str:
        """Get the system prompt for content generation based on reasoning."""
        return '''
**ROLE:** Expert Government Proposal Writer with Chain of Thought Implementation

**MISSION:** Generate high-quality proposal section content based on detailed reasoning analysis and strategic planning.

**CRITICAL REQUIREMENTS:**
- Follow the reasoning analysis and content strategy exactly
- Generate content that directly addresses all identified requirements
- Ensure compliance with all mandatory standards
- Use professional government proposal language and formatting
- NO placeholders, brackets, TBD, or incomplete information
- Return ONLY the section content - no titles, explanations, or metadata

**CONTENT STANDARDS:**
- Professional, clear, and compelling writing
- Specific, measurable, and actionable statements
- Government-appropriate terminology and tone
- Structured with appropriate headers and formatting
- Evidence-based claims with specific examples
- Compliance with all identified requirements

**QUALITY TARGETS:**
- Technical Excellence: 95%+
- Compliance Adherence: 100%
- Evaluation Readiness: 95%+
- Professional Presentation: 95%+
'''

    def _build_content_generation_prompt(
        self,
        section_title: str,
        section_number: str,
        reasoning_analysis: Dict[str, Any],
        content_strategy: Dict[str, Any],
        rfp_context: str,
        client_context: str,
        tenant_metadata: str,
        personnel_context: str,
        is_cover_letter: bool
    ) -> str:
        """Build the content generation prompt based on reasoning analysis."""

        return f'''
**CONTENT GENERATION TASK:**
Generate content for: {section_number} {section_title}

**REASONING ANALYSIS TO IMPLEMENT:**
{json.dumps(reasoning_analysis, indent=2)}

**CONTENT STRATEGY TO FOLLOW:**
{json.dumps(content_strategy, indent=2)}

**AVAILABLE CONTEXT:**

**RFP Requirements:**
{rfp_context[:2000] if rfp_context else "No RFP context available"}

**Client Capabilities:**
{client_context[:1000] if client_context else "No client context available"}

**Company Information:**
{tenant_metadata[:800] if tenant_metadata else "No company information available"}

**Personnel Information:**
{personnel_context[:1000] if personnel_context else "No personnel information available"}

**GENERATION INSTRUCTIONS:**
Based on the reasoning analysis and content strategy above, generate professional proposal content that:

1. **Implements the Strategy:** Follow the content strategy exactly as analyzed
2. **Addresses Requirements:** Cover all requirements identified in the analysis
3. **Ensures Compliance:** Meet all compliance standards identified
4. **Maximizes Quality:** Achieve the quality targets specified in the analysis
5. **Uses Available Context:** Leverage all relevant context information provided

**SPECIFIC REQUIREMENTS:**
- Generate {"a professional business letter" if is_cover_letter else "technical proposal section content"}
- Follow the narrative structure recommended in the content strategy
- Include all key messages and evidence points identified
- Use the formatting and organization approach specified
- Ensure all compliance requirements are met
- NO explanatory text - return only the final content

Generate the complete section content now:
'''

    async def _validate_generated_content(
        self,
        content: str,
        reasoning_analysis: Dict[str, Any],
        compliance_requirements: Dict[str, Any],
        page_limit: int
    ) -> Dict[str, Any]:
        """Validate the generated content against reasoning analysis and requirements."""

        validation_results = {
            'passed': True,
            'issues': [],
            'quality_score': 0,
            'compliance_score': 0,
            'recommendations': []
        }

        try:
            # Basic content validation
            if not content or len(content.strip()) < 100:
                validation_results['issues'].append("Content is too short")
                validation_results['passed'] = False

            # Word count validation (approximate page limit check)
            word_count = len(content.split())
            estimated_pages = word_count / 250  # Rough estimate: 250 words per page

            if estimated_pages > page_limit * 1.2:  # Allow 20% buffer
                validation_results['issues'].append(f"Content may exceed page limit: {estimated_pages:.1f} pages vs {page_limit} limit")

            # Check for placeholders or incomplete content
            placeholder_indicators = ['[', ']', 'TBD', 'TODO', 'PLACEHOLDER', 'XXX', '{{', '}}']
            for indicator in placeholder_indicators:
                if indicator in content:
                    validation_results['issues'].append(f"Content contains placeholder indicator: {indicator}")
                    validation_results['passed'] = False

            # Calculate quality score based on content characteristics
            quality_score = self._calculate_content_quality_score(content, reasoning_analysis)
            validation_results['quality_score'] = quality_score

            # Calculate compliance score
            compliance_score = self._calculate_compliance_score(content, compliance_requirements)
            validation_results['compliance_score'] = compliance_score

            # Overall validation
            if quality_score < 70 or compliance_score < 80:
                validation_results['passed'] = False
                validation_results['recommendations'].append("Content quality or compliance below acceptable thresholds")

            logger.info(f"COT: Content validation completed - Quality: {quality_score}, Compliance: {compliance_score}")

        except Exception as e:
            logger.error(f"COT: Content validation failed: {e}")
            validation_results['passed'] = False
            validation_results['issues'].append(f"Validation error: {str(e)}")

        return validation_results

    def _calculate_content_quality_score(self, content: str, reasoning_analysis: Dict[str, Any]) -> int:
        """Calculate a quality score for the generated content."""
        score = 100

        # Length check
        word_count = len(content.split())
        if word_count < 200:
            score -= 20
        elif word_count < 100:
            score -= 40

        # Structure check (headers, bullets, etc.)
        if '##' not in content and '**' not in content and '•' not in content:
            score -= 10  # Deduct for lack of structure

        # Professional language check (basic)
        professional_indicators = ['demonstrate', 'implement', 'ensure', 'provide', 'deliver', 'manage']
        found_indicators = sum(1 for indicator in professional_indicators if indicator.lower() in content.lower())
        if found_indicators < 3:
            score -= 15

        # Specific vs generic content
        generic_phrases = ['we will', 'our team', 'our company', 'we provide', 'we offer']
        generic_count = sum(1 for phrase in generic_phrases if phrase.lower() in content.lower())
        if generic_count > 5:
            score -= 10

        return max(0, min(100, score))

    def _calculate_compliance_score(self, content: str, compliance_requirements: Dict[str, Any]) -> int:
        """Calculate a compliance score for the generated content."""
        if not compliance_requirements:
            return 85  # Default score when no specific requirements

        score = 100

        # Check for required elements (this is a simplified check)
        # In a real implementation, this would be more sophisticated
        required_elements = compliance_requirements.get('required_elements', [])
        for element in required_elements:
            if isinstance(element, str) and element.lower() not in content.lower():
                score -= 10

        # Check for prohibited content
        prohibited_elements = compliance_requirements.get('prohibited_elements', [])
        for element in prohibited_elements:
            if isinstance(element, str) and element.lower() in content.lower():
                score -= 15

        return max(0, min(100, score))

    async def _should_use_chain_of_thought(
        self,
        section_title: str,
        section_description: str,
        page_limit: int,
        compliance_requirements: Dict[str, Any]
    ) -> bool:
        """
        Intelligently determine if a section should use chain of thought reasoning using LLM analysis.

        Uses AI to assess section complexity, strategic importance, and reasoning requirements
        rather than relying on primitive keyword matching.
        """

        # Quick heuristic checks first (avoid LLM call for obvious cases)
        if page_limit >= 8:  # Very high page limits definitely need COT
            return True

        if page_limit <= 1 and len(section_description.split()) <= 20:  # Simple sections
            return False

        # Use LLM for intelligent complexity assessment
        try:
            complexity_score = await self._assess_section_complexity_with_llm(
                section_title, section_description, page_limit, compliance_requirements
            )

            # Use COT if complexity score is above threshold
            return complexity_score >= 70  # 70+ indicates high complexity/strategic importance

        except Exception as e:
            logger.warning(f"COT: LLM complexity assessment failed, using fallback logic: {e}")
            # Fallback to basic heuristics if LLM fails
            return self._fallback_complexity_assessment(
                section_title, section_description, page_limit, compliance_requirements
            )

    async def _assess_section_complexity_with_llm(
        self,
        section_title: str,
        section_description: str,
        page_limit: int,
        compliance_requirements: Dict[str, Any]
    ) -> int:
        """
        Use LLM to intelligently assess section complexity and strategic importance.

        Returns a complexity score from 0-100 where:
        - 0-30: Simple sections (cover letters, basic info)
        - 31-69: Moderate complexity (standard sections)
        - 70-100: High complexity (strategic, technical, complex reasoning required)
        """

        complexity_prompt = f'''
**TASK:** Assess the complexity and strategic importance of this proposal section to determine if it requires sophisticated chain-of-thought reasoning.

**SECTION TO ANALYZE:**
Title: {section_title}
Description: {section_description}
Page Limit: {page_limit} pages
Compliance Requirements: {len(compliance_requirements) if compliance_requirements else 0} requirements

**ASSESSMENT CRITERIA:**

**HIGH COMPLEXITY (70-100 points) - Requires Chain of Thought:**
- Strategic sections requiring multi-step analysis (technical approach, solution architecture)
- Complex technical content requiring systematic reasoning
- Sections with multiple interdependent requirements
- High-stakes evaluation sections (past performance, management approach)
- Content requiring innovation, differentiation, or competitive positioning
- Sections needing compliance verification across multiple standards
- Multi-faceted content requiring careful balance of competing priorities

**MODERATE COMPLEXITY (31-69 points) - May benefit from Chain of Thought:**
- Standard technical sections with clear requirements
- Sections with moderate compliance requirements
- Content requiring some strategic thinking but straightforward approach
- Sections with established templates or patterns

**LOW COMPLEXITY (0-30 points) - Standard generation sufficient:**
- Simple administrative sections (cover letters, basic company info)
- Straightforward factual content
- Sections with minimal requirements or evaluation weight
- Template-based content with little customization needed

**ANALYSIS FACTORS:**
1. **Strategic Importance:** How critical is this section for winning?
2. **Technical Complexity:** How sophisticated is the required content?
3. **Reasoning Requirements:** Does this need multi-step logical analysis?
4. **Compliance Complexity:** How many standards/requirements must be balanced?
5. **Evaluation Impact:** How heavily weighted is this section in scoring?
6. **Innovation Opportunity:** Does this section benefit from creative problem-solving?

**OUTPUT:** Return only a single integer from 0-100 representing the complexity score.
'''

        try:
            messages = [
                ("system", "You are an expert proposal strategist who assesses section complexity for optimal generation approaches. Analyze the section and return only a numeric complexity score from 0-100."),
                ("human", complexity_prompt)
            ]

            result = await asyncio.wait_for(
                asyncio.to_thread(self.llm.invoke, messages),
                timeout=30.0  # Quick assessment
            )

            response = str(result.content).strip()

            # Extract numeric score from response
            import re
            score_match = re.search(r'\b(\d{1,3})\b', response)
            if score_match:
                score = int(score_match.group(1))
                # Ensure score is in valid range
                score = max(0, min(100, score))
                logger.info(f"COT: LLM complexity assessment for '{section_title}': {score}/100")
                return score
            else:
                logger.warning(f"COT: Could not extract numeric score from LLM response: {response}")
                return 50  # Default moderate complexity

        except Exception as e:
            logger.error(f"COT: LLM complexity assessment failed: {e}")
            raise

    def _fallback_complexity_assessment(
        self,
        section_title: str,
        section_description: str,
        page_limit: int,
        compliance_requirements: Dict[str, Any]
    ) -> bool:
        """
        Fallback complexity assessment using heuristics when LLM is unavailable.
        """

        # High page limits indicate complexity
        if page_limit >= 5:
            return True

        # Multiple compliance requirements indicate complexity
        if compliance_requirements and len(compliance_requirements) > 3:
            return True

        # Long descriptions often indicate complexity
        if len(section_description.split()) > 50:
            return True

        # Strategic keywords as last resort (but smarter than before)
        strategic_indicators = [
            'technical', 'approach', 'methodology', 'management', 'architecture',
            'performance', 'strategy', 'implementation', 'solution', 'innovation',
            'compliance', 'security', 'quality', 'risk', 'evaluation'
        ]

        text_to_check = f"{section_title} {section_description}".lower()
        strategic_matches = sum(1 for indicator in strategic_indicators if indicator in text_to_check)

        # Need multiple strategic indicators to trigger COT
        return strategic_matches >= 3

    async def assess_section_complexity(
        self,
        section_title: str,
        section_description: str,
        page_limit: int,
        compliance_requirements: Dict[str, Any]
    ) -> int:
        """
        Public method to assess section complexity using LLM analysis.

        Returns a complexity score from 0-100.
        """
        return await self._assess_section_complexity_with_llm(
            section_title, section_description, page_limit, compliance_requirements
        )

    async def should_use_chain_of_thought(
        self,
        section_title: str,
        section_description: str,
        page_limit: int,
        compliance_requirements: Dict[str, Any]
    ) -> bool:
        """
        Public method to determine if chain of thought should be used.
        """
        return await self._should_use_chain_of_thought(
            section_title, section_description, page_limit, compliance_requirements
        )


class ChainOfThoughtIntegration:
    """
    Integration class to seamlessly integrate chain of thought reasoning
    into the existing ProposalOutlineService.
    """

    def __init__(self, chain_of_thought_service: ChainOfThoughtService):
        self.cot_service = chain_of_thought_service

    async def enhance_draft_generation_with_cot(
        self,
        section_title: str,
        section_description: str,
        section_number: str,
        page_limit: int,
        rfp_context: str,
        client_context: str,
        tenant_metadata: str,
        compliance_requirements: Dict[str, Any],
        previous_sections_summary: str = "",
        is_cover_letter: bool = False,
        is_personnel_section: bool = False,
        personnel_context: str = ""
    ) -> Dict[str, Any]:
        """
        Enhanced draft generation that uses chain of thought reasoning.

        This method can be called from the existing ProposalOutlineService
        to add chain of thought capabilities to section generation.
        """

        logger.info(f"COT_INTEGRATION: Enhancing draft generation for '{section_title}' with chain of thought")

        try:
            # Use chain of thought for complex sections
            should_use_cot = await self.cot_service._should_use_chain_of_thought(
                section_title, section_description, page_limit, compliance_requirements
            )

            if should_use_cot:
                logger.info(f"COT_INTEGRATION: Using chain of thought for '{section_title}'")

                result = await self.cot_service.generate_section_with_chain_of_thought(
                    section_title=section_title,
                    section_description=section_description,
                    section_number=section_number,
                    page_limit=page_limit,
                    rfp_context=rfp_context,
                    client_context=client_context,
                    tenant_metadata=tenant_metadata,
                    compliance_requirements=compliance_requirements,
                    previous_sections_summary=previous_sections_summary,
                    is_cover_letter=is_cover_letter,
                    is_personnel_section=is_personnel_section,
                    personnel_context=personnel_context
                )

                if result.get('success', False):
                    logger.info(f"COT_INTEGRATION: Chain of thought generation successful for '{section_title}'")
                    return {
                        'success': True,
                        'content': result['content'],
                        'enhanced_with_cot': True,
                        'reasoning_applied': True,
                        'metadata': result['metadata']
                    }
                else:
                    logger.warning(f"COT_INTEGRATION: Chain of thought failed for '{section_title}', falling back")
                    return {
                        'success': False,
                        'enhanced_with_cot': False,
                        'fallback_needed': True,
                        'error': result.get('error', 'Unknown error')
                    }
            else:
                logger.info(f"COT_INTEGRATION: Section '{section_title}' doesn't require chain of thought")
                return {
                    'success': False,
                    'enhanced_with_cot': False,
                    'cot_not_needed': True,
                    'reason': 'Section complexity does not warrant chain of thought reasoning'
                }

        except Exception as e:
            logger.error(f"COT_INTEGRATION: Integration failed for '{section_title}': {e}")
            return {
                'success': False,
                'enhanced_with_cot': False,
                'error': str(e),
                'fallback_needed': True
            }
