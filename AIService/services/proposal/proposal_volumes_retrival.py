from typing import List, Dict, Any
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from controllers.customer.proposals_format_queue_controller import ProposalsFormatQueueController
from controllers.customer.proposals_in_review_controller import ProposalsInReviewController
import json
from services.proposal.proposal_decoding_service import ProposalDecodingService

from cryptography.exceptions import InvalidKey, InvalidTag
from cryptography.hazmat.primitives.asymmetric.padding import OAEP, MGF1
from cryptography.hazmat.primitives import hashes
from collections import defaultdict
from models.customer_models import ProposalsInReview, ProposalsFormatQueue
from sqlalchemy import select

class ProposalVolumeRetrievalService:
    """Service for retrieving and decrypting proposal volumes from review and format queues."""
    
    def __init__(self):
        self.proposal_decoding_service = ProposalDecodingService()
    
    async def get_all_volumes_from_review(
        self,
        db: AsyncSession,
        tenant_id: str,
        opportunity_id: str
    ) -> List[List[Dict[str, Any]] | None]:
        """
        Retrieve all volumes from ProposalsInReview for a given tenant_id and opportunity_id.
        Returns a list of volumes, where each volume is a list of dictionaries or None if empty.
        """
        logger.info(f"get_all_volumes_from_review called with tenant_id={tenant_id}, opportunity_id={opportunity_id}")

       # Query all sections for this opportunity and tenant, latest version only
        current_version = await ProposalsInReviewController.get_current_version(db, tenant_id, opportunity_id)
        if not current_version:
            logger.info(f"No version found for tenant_id={tenant_id}, opportunity_id={opportunity_id}")
            return []

        # Get all sections for the latest version
        query = await db.execute(
            select(ProposalsInReview).where(
                ProposalsInReview.opps_id == opportunity_id,
                ProposalsInReview.tenant_id == tenant_id,
                ProposalsInReview.version == current_version
            )
        )
        sections = list(query.scalars().all())
        if not sections:
            logger.info(f"No sections found for tenant_id={tenant_id}, opportunity_id={opportunity_id}, version={current_version}")
            return []

        # Get encryption key info
        key_info = await self.proposal_decoding_service.get_key_pair(db, tenant_id)
        if not key_info or not key_info.get("passphrase"):
            logger.error(f"No encryption key info found for tenant_id={tenant_id}")
            return []

        passphrase = key_info["passphrase"]

        # Group sections by volume_number
        volumes = defaultdict(list)
        for section in sections:
            try:
                # Decrypt section data
                try:
                    decrypted_data = await self.proposal_decoding_service.decrypt_section(
                        db, tenant_id, section.proposal_data, passphrase
                    )
                except ValueError:
                    decrypted_data = self.proposal_decoding_service.decrypt_with_passphrase(
                        section.proposal_data, passphrase
                    )
                section_data = json.loads(decrypted_data.decode('utf-8'))
                volumes[section.volume_number].append(section_data)
            except Exception as e:
                logger.error(f"Failed to decrypt/parse section {section.section_number} in volume {section.volume_number}: {str(e)}")

        # Sort volumes by volume_number, and sections by section_number (if needed)
        all_volumes = []
        for vol_num in sorted(volumes.keys()):
            # Sort sections by section_number (as string, or convert to int if numeric)
            sorted_sections = sorted(
                volumes[vol_num],
                key=lambda s: (
                    str(s.get("section_number") or s.get("number") or "")
                )
            )
            all_volumes.append(sorted_sections)

        logger.info(f"Retrieved {len(all_volumes)} volumes for tenant_id={tenant_id}, opportunity_id={opportunity_id}")
        return all_volumes

    async def get_all_volumes_from_format(
        self,
        db: AsyncSession,
        tenant_id: str,
        opportunity_id: str
    ) -> List[List[Dict[str, Any]] | None]:
        """
        Retrieve all volumes from ProposalsFormatQueue for a given tenant_id and opportunity_id.
        Returns a list of volumes, where each volume is a list of dictionaries or None if empty.
        """
        logger.info(f"get_all_volumes_from_format called with tenant_id={tenant_id}, opportunity_id={opportunity_id}")
        
        try:
            # Query proposals in format queue by opportunity ID
            proposals = await ProposalsFormatQueueController.get_by_opps_id(db, opportunity_id)
            
            if not proposals:
                logger.info(f"No proposals found in format queue for tenant_id={tenant_id}, opportunity_id={opportunity_id}")
                return []

            # Get the maximum version to ensure we fetch the latest data
            current_version = await ProposalsFormatQueueController.get_current_version(db, tenant_id, opportunity_id)
            if current_version is None:
                logger.error(f"Could not determine current version for tenant_id={tenant_id}, opportunity_id={opportunity_id}")
                return []

            # Filter proposals by the latest version
            latest_proposals = [p for p in proposals if p.version == current_version]
            
            if not latest_proposals:
                logger.info(f"No proposals found for version {current_version}")
                return []

            # Process the latest proposal record
            proposal = latest_proposals[0]
            try:
                # Enhanced encryption details retrieval
                key_info = await self.proposal_decoding_service.get_key_pair(db, tenant_id)
                if not key_info or not key_info.get("passphrase"):
                    logger.error(f"No encryption key info or passphrase found for tenant_id={tenant_id}")
                    return [None]

                passphrase = key_info["passphrase"]
                
                # Enhanced passphrase verification
                passphrase_valid = await self.proposal_decoding_service.verify_passphrase(db, tenant_id, passphrase)
                if not passphrase_valid:
                    logger.error(f"Invalid passphrase for tenant_id={tenant_id}")
                    return [None]

                # Validate encrypted data length
                key_size_bytes = 4096 // 8  # RSA key size in bytes
                min_data_length = 16 + key_size_bytes  # IV (16 bytes) + encrypted AES key
                if len(proposal.proposal_data) < min_data_length:
                    logger.error(f"Invalid proposal_data length for opportunity_id={opportunity_id}: {len(proposal.proposal_data)} bytes (minimum required: {min_data_length})")
                    return [None]

                # Decrypt the proposal_data
                decrypted_data = await self.proposal_decoding_service.decrypt_section(
                    db, tenant_id, proposal.proposal_data, passphrase
                )
                
                # Decode the JSON data
                volume_data = json.loads(decrypted_data.decode('utf-8'))
                
                all_volumes: List[List[Dict[str, Any]] | None] = [volume_data]
                
                logger.info(f"Successfully retrieved and decrypted volumes from format queue for tenant_id={tenant_id}, opportunity_id={opportunity_id}")
                return all_volumes
                
            except (ValueError, InvalidKey, InvalidTag) as e:
                logger.error(f"Decryption failed for opportunity_id={opportunity_id}: {str(e)}")
                return [None]
            except json.JSONDecodeError as e:
                logger.error(f"JSON decoding failed for opportunity_id={opportunity_id}: {str(e)}")
                return [None]
            except Exception as e:
                logger.error(f"Unexpected error decoding proposal data for opportunity_id={opportunity_id}: {str(e)}")
                return [None]

        except Exception as e:
            logger.error(f"Error retrieving volumes from format queue for tenant_id={tenant_id}, opportunity_id={opportunity_id}: {str(e)}", exc_info=True)
            return []
        