# ChromaDB has been completely replaced with Qdrant
# This file maintains the same interface but uses Qdrant under the hood

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple
import asyncio

from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import (
    Distance, VectorParams, CollectionStatus, PointStruct,
    Filter, FieldCondition, Range, MatchValue
)

from utils.hashing import generate_hash
from controllers.kontratar.chromadb_mapping_controller import ChromaDBMappingController

from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from utils.embedding_model import KontratarEmbeddings
import random
from database import get_kontratar_db
from config import settings


# Utility functions for extracting IDs from collection names (same as before)
def extract_opportunity_id(collection_name: str) -> str:
    logger.debug(f"extract_opportunity_id called with collection_name: {collection_name} (type: {type(collection_name)})")
    base_name = collection_name.split(".")[0]
    logger.debug(f"base_name: {base_name} (type: {type(base_name)})")
    parts = base_name.split("_")
    logger.debug(f"parts: {parts} (type: {type(parts)})")
    if len(parts) == 1:
        result = parts[0]
        logger.debug(f"Returning single part result: {result} (type: {type(result)})")
        return result
    elif len(parts) >= 2:
        result = parts[1]
        logger.debug(f"Returning second part result: {result} (type: {type(result)})")
        return result
    raise ValueError(f"Invalid collection name format: {collection_name}")

def extract_tenant_id(collection_name: str) -> str:
    logger.debug(f"extract_tenant_id called with collection_name: {collection_name} (type: {type(collection_name)})")
    base_name = collection_name.split(".")[0]
    logger.debug(f"base_name: {base_name} (type: {type(base_name)})")
    parts = base_name.split("_")
    logger.debug(f"parts: {parts} (type: {type(parts)})")
    if len(parts) == 1:
        result = "SYSTEM"
        logger.debug(f"Returning default SYSTEM result: {result} (type: {type(result)})")
        return result
    elif len(parts) >= 2:
        result = parts[0]
        logger.debug(f"Returning first part result: {result} (type: {type(result)})")
        return result
    raise ValueError(f"Invalid collection name format: {collection_name}")

def extract_suffix(collection_name: str) -> Optional[str | int]:
    logger.debug(f"extract_suffix called with collection_name: {collection_name} (type: {type(collection_name)})")
    parts = collection_name.split(".")
    logger.debug(f"Split collection_name into parts: {parts} (length: {len(parts)})")

    if len(parts) <= 1:
        logger.info(f"No suffix found in collection_name: {collection_name}")
        return None

    try:
        suffix = int(parts[-1])
        logger.info(f"Extracted numeric suffix: {suffix} from collection_name: {collection_name}")
        return suffix
    except Exception as e:
        logger.info(f"Suffix is not numeric, returning as string: {parts[-1]} from collection_name: {collection_name}")
        return parts[-1]
        

class ChromaService:
    """
    ChromaService - Now powered by Qdrant!
    Maintains the same interface as the original ChromaService but uses Qdrant as the backend.
    """
    def __init__(self, embedding_api_url: str, embedding_api_key: Optional[str] = None):
        self.embeddings = KontratarEmbeddings(embedding_api_url, embedding_api_key)
        self.vector_db_service = ChromaDBMappingController()  # Reusing existing mapping table
        self.qdrant_url = settings.qdrant_url
        self.client = QdrantClient(url=self.qdrant_url)
        
        # Constants for Qdrant configuration
        self.VECTOR_SIZE = 1536  
        self.DISTANCE_METRIC = Distance.COSINE

    async def get_chroma_url(self, db: AsyncSession, unique_id: str, tenant_id: str) -> Optional[str]:
        # For backward compatibility, return Qdrant URL
        return self.qdrant_url

    async def get_relevant_chunks(self, db: AsyncSession, collection_name: str, query: str, n_results: int = 5) -> List[str]:
        result = await self.get_relevant_chunks_rest(db, collection_name, query, n_results)
        if result is None:
            return []
        return result
    
    async def convert_string_to_embeddings(self, text: str) -> List[float]:
        return self.embeddings.embed_query(text)
    
    async def create_collection(self, chroma_url: str, collection_name: str, collection_metadata: Dict[str, Any]) -> Optional[str]:
        """Create a Qdrant collection with the given name and metadata"""
        try:
            # Check if collection already exists
            existing_collections = self.client.get_collections().collections
            if any(col.name == collection_name for col in existing_collections):
                logger.info(f"Collection {collection_name} already exists")
                return collection_name

            # Create collection with vector configuration
            self.client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=self.VECTOR_SIZE,
                    distance=self.DISTANCE_METRIC
                )
            )
            
            logger.info(f"Created Qdrant collection: {collection_name}")
            return collection_name

        except Exception as e:
            logger.error(f"Error creating Qdrant collection '{collection_name}': {e}")
            return None

    async def add_documents_to_collection(self, chroma_url: str, collection_id: str, documents: List[str], embeddings: List[List[float]], metadatas: Optional[List[Dict[str, Any]]] = None) -> Optional[Tuple[List[str], List[List[float]]]]:
        """Add documents to a Qdrant collection"""
        try:
            logger.info(f"Preparing to add {len(documents)} documents to collection {collection_id}")
            
            # Generate unique IDs for each document
            ids = [str(uuid.uuid4()) for _ in documents]
            logger.debug(f"Generated document IDs: {ids}")

            # Prepare points for Qdrant
            points = []
            for i, (doc_id, document, embedding) in enumerate(zip(ids, documents, embeddings)):
                payload = {
                    "document": document,
                    "document_index": i
                }
                
                # Add metadata if provided
                if metadatas and i < len(metadatas):
                    payload.update(metadatas[i])
                
                point = PointStruct(
                    id=doc_id,
                    vector=embedding,
                    payload=payload
                )
                points.append(point)

            # Upload points to Qdrant
            self.client.upsert(
                collection_name=collection_id,
                points=points
            )

            logger.info(f"Successfully added {len(documents)} documents to collection {collection_id}")
            return documents, embeddings
            
        except Exception as e:
            logger.error(f"Error adding documents to collection {collection_id}: {e}")
            return None
            
    async def select_least_loaded_instance(self, db) -> str:
        """For single Qdrant instance, just return the configured URL"""
        return self.qdrant_url

    async def count_active_collections_by_instance_url(self, db, instance_url: str) -> int:
        """Count the number of active collections for the Qdrant instance"""
        from models.kontratar_models import ChromaDBInstanceMapping
        from sqlalchemy import select, func

        # For now, reuse the existing mapping table structure
        query = select(func.count()).select_from(ChromaDBInstanceMapping).where(
            ChromaDBInstanceMapping.chroma_instance_url == instance_url,
            ChromaDBInstanceMapping.status == "ACTIVE"
        )
        result = await db.execute(query)
        return result.scalar() or 0

    async def add_documents(self, db: AsyncSession, collection_name: str, documents: List[str], metadatas: Optional[List[Dict[str, Any]]] = None) -> Optional[Tuple[List[str], List[List[float]]]]:
        try:
            logger.info(f"Starting to add documents to collection: {collection_name}")
            
            unique_id = extract_opportunity_id(collection_name)
            tenant_id = extract_tenant_id(collection_name)
            logger.debug(f"Extracted unique_id: {unique_id}, tenant_id: {tenant_id}")

            vector_db_instance = await self.vector_db_service.get_by_unique_id_and_tenant(db, unique_id, tenant_id)
            
            if vector_db_instance is None:
                # Create new mapping (reusing existing table structure but storing Qdrant URL)
                from models.kontratar_models import ChromaDBInstanceMapping
                vector_db_instance = ChromaDBInstanceMapping(
                    unique_id=unique_id,
                    tenant_id=tenant_id,
                    chroma_instance_url=self.qdrant_url,
                    collection_name=collection_name,
                    created_date=datetime.utcnow(),
                    status="ACTIVE"
                )
                db.add(vector_db_instance)
                await db.commit()
                await db.refresh(vector_db_instance)
                logger.info(f"Created new Qdrant mapping for {unique_id}, {tenant_id} with collection name {collection_name}")
            else:
                logger.info(f"Found existing mapping for {unique_id}, {tenant_id} with collection name {vector_db_instance.collection_name}")
            
            db_collection_name = str(vector_db_instance.collection_name)
            
            # Generate hash for collection name if needed
            if db_collection_name == f"{tenant_id}_{unique_id}" or db_collection_name == unique_id:
                logger.info(f"Creating hash for collection name")
                db_collection_name = generate_hash(collection_name)
                logger.info(f"Generated new collection name: {db_collection_name} for unique_id: {unique_id}, tenant_id: {tenant_id}")

                # Update collection name with hash value
                await self.vector_db_service.update_by_unique_id_and_tenant(
                    db, unique_id, tenant_id, collection_name=db_collection_name,
                )

            # Handle versioning
            collection_version = int(str(vector_db_instance.version)) + 1 if vector_db_instance.version is not None else 0
            latest_collection_name = db_collection_name

            if collection_version > 0:
                latest_collection_name = db_collection_name + "." + str(collection_version)
                logger.debug(f"Using versioned collection name: {latest_collection_name}")
            
            logger.info(f"Generating embeddings for {len(documents)} documents")
            embeddings = self.embeddings.embed_documents(documents)
            logger.debug(f"Generated {len(embeddings)} embeddings")
            
            # Create collection metadata
            collection_metadata = {
                "tenant_id": tenant_id,
                "unique_id": unique_id
            }

            logger.debug(f"Creating collection {latest_collection_name} with metadata: {collection_metadata}")
            collection_id = await self.create_collection(self.qdrant_url, latest_collection_name, collection_metadata)
            logger.info(f"Collection ID for {self.qdrant_url}, {latest_collection_name}: {collection_id}")

            # Add documents to collection
            await self.add_documents_to_collection(self.qdrant_url, collection_id, documents, embeddings, metadatas)

            # Update version in database
            await self.vector_db_service.update_version(db, unique_id, tenant_id, collection_version)
            
            logger.info(f"Successfully added documents to collection: {latest_collection_name}")
            return documents, embeddings
            
        except Exception as e:
            logger.error(f"Error in add_documents: {e}")
            return None

    async def ensure_collection_exists(self, db: AsyncSession, collection_name: str) -> bool:
        logger.debug(f"ensure_collection_exists called with collection_name: {collection_name}")
        unique_id = extract_opportunity_id(collection_name)
        tenant_id = extract_tenant_id(collection_name)
        logger.info(f"Extracted unique_id: {unique_id}, tenant_id: {tenant_id} from collection_name: {collection_name}")
        
        try:
            # Check if collection exists in Qdrant
            existing_collections = self.client.get_collections().collections
            exists = any(col.name == collection_name for col in existing_collections)
            logger.info(f"Collection '{collection_name}' existence: {exists}")
            return exists
        except Exception as e:
            logger.error(f"Error checking collection existence: {e}")
            return False

    async def list_collections(self, db: AsyncSession, unique_id: str, tenant_id: str) -> List[str]:
        try:
            collections = self.client.get_collections().collections
            collection_names = [col.name for col in collections]
            logger.info(f"Found {len(collection_names)} collections in Qdrant")
            return collection_names
        except Exception as e:
            logger.error(f"Error listing collections: {e}")
        return []

    async def get_collection_information_by_name(self, db: AsyncSession, collection_name: str) -> Optional[Dict[str, Any]]:
        try:
            collection_info = self.client.get_collection(collection_name)
            return {
                "name": collection_info.config.name if collection_info.config else collection_name,
                "vectors_count": collection_info.vectors_count,
                "status": collection_info.status.value if collection_info.status else "unknown"
            }
        except Exception as e:
            logger.error(f"Error getting collection information for {collection_name}: {e}")
        return None

    async def delete_collection(self, db: AsyncSession, collection_name: str) -> bool:
        try:
            self.client.delete_collection(collection_name)
            logger.info(f"Deleted collection {collection_name} from Qdrant")
            return True
        except Exception as e:
            logger.error(f"Failed to delete collection {collection_name}: {e}")
            return False

    async def update_collection(self, db: AsyncSession, collection_name: str, new_metadata: Dict[str, Any]) -> bool:
        # Qdrant doesn't support direct metadata updates on collections
        logger.warning(f"Collection metadata update not directly supported in Qdrant for {collection_name}")
        return False

    async def delete_collection_content(self, db: AsyncSession, collection_name: str) -> bool:
        try:
            # Delete all points in the collection
            self.client.delete(
                collection_name=collection_name,
                points_selector=models.FilterSelector(
                    filter=models.Filter()  # Empty filter deletes all points
                )
            )
            logger.info(f"Deleted all content from collection {collection_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete collection content for {collection_name}: {e}")
        return False
    
    async def collection_exists(self, collection_name: str, chroma_url: str) -> bool:
        logger.debug("Entering the function - collection_exists()")
        try:
            existing_collections = self.client.get_collections().collections
            exists = any(col.name == collection_name for col in existing_collections)
            logger.info(f"Collection existence check for {collection_name}: {exists}")
            return exists
        except Exception as e:
            logger.error(f"Error checking collection existence {collection_name}: {e}")
            return False
        finally:
            logger.debug("Exiting the function - collection_exists()")

    async def get_current_collection_version(self, base_collection_name: str, chroma_url: str) -> int:
        logger.info(f"Finding current version for collection base name: {base_collection_name}")
        current_version = -1

        try:
            # Get all collections
            collections = self.client.get_collections().collections
            collection_names = [col.name for col in collections]
            
            # First check if base collection exists (no version suffix)
            hashed_base = generate_hash(base_collection_name)
            if hashed_base in collection_names:
                logger.info(f"Base collection {base_collection_name} exists")
                current_version = 0
            else:
                # Check for numbered versions starting from 1
                version_to_check = 1
                max_versions = 1000

                while version_to_check <= max_versions:
                    versioned_name = f"{hashed_base}.{version_to_check}"
                    if versioned_name in collection_names:
                        current_version = version_to_check
                        version_to_check += 1
                    else:
                        break

                if version_to_check > max_versions:
                    logger.warning(f"Reached maximum version limit ({max_versions}) for {base_collection_name}")

            logger.info(f"Current version found for {base_collection_name}: {current_version}")
            return current_version
        except Exception as e:
            logger.error(f"Error finding current collection version for {base_collection_name}: {e}")
            return -1

    async def get_next_collection_version(self, base_collection_name: str, chroma_url: str) -> int:
        logger.info(f"Finding next version for collection base name: {base_collection_name}")
        current_version = await self.get_current_collection_version(base_collection_name, chroma_url)
        next_version = 0 if current_version == -1 else current_version + 1
        logger.info(f"Next version for {base_collection_name} is {next_version}")
        return next_version

    async def get_latest_collection_name(self, base_collection_name: str, chroma_url: str) -> Optional[str]:
        """Returns the name of the latest versioned collection for a base name, or None if none exist."""
        logger.info(f"Getting latest collection name for base name: {base_collection_name}")
        try:
            current_version = await self.get_current_collection_version(base_collection_name, chroma_url)
            if current_version == -1:
                logger.info(f"No collection exists for base name: {base_collection_name}")
                return None
            elif current_version == 0:
                hashed_name = generate_hash(base_collection_name)
                logger.info(f"Latest collection for base {base_collection_name} is hashed collection: {hashed_name}")
                return hashed_name
            else:
                latest_name = f"{generate_hash(base_collection_name)}.{current_version}"
                logger.info(f"Latest collection is: {latest_name}")
                return latest_name
        except Exception as e:
            logger.error(f"Error finding latest collection version for {base_collection_name}: {e}")
            return None

    async def get_relevant_chunks_rest(
        self,
        db: AsyncSession,
        collection_name: str,
        query: str,
        n_results: int = 5
    ) -> Optional[List[str]]:
        """Retrieve relevant chunks from Qdrant using similarity search."""
        try:
            return await asyncio.wait_for(
                self._get_relevant_chunks_rest_impl(db, collection_name, query, n_results),
                timeout=600  # 10 minutes
            )
        except asyncio.TimeoutError:
            logger.warning(f"Qdrant operation timeout for collection {collection_name} - continuing without context")
            return []
        except Exception as e:
            logger.error(f"Error: {e}")
            return []
        
    async def _get_relevant_chunks_rest_impl(
        self,
        db: AsyncSession,
        collection_name: str,
        query: str,
        n_results: int = 5
    ) -> Optional[List[str]]:
        """Internal implementation of Qdrant retrieval."""
        unique_id = extract_opportunity_id(collection_name)
        tenant_id = extract_tenant_id(collection_name)
        suffix = extract_suffix(collection_name)

        if suffix and isinstance(suffix, str):
            unique_id = unique_id + "." + suffix

        # Handle case when db is None (no database access)
        if db is None:
            logger.info(f"No database session provided, checking for direct collection: {collection_name}")
            if await self.collection_exists(collection_name, self.qdrant_url):
                logger.info(f"Using direct collection name: {collection_name}")
                latest_collection_name = collection_name
            else:
                logger.warning(f"No collection found for {collection_name}")
                return None
        else:
            vector_db_instance = await self.ensure_chroma_instance_exists(db, unique_id, tenant_id, collection_name)
            if vector_db_instance is None:
                # Fallback: check if the direct collection name exists
                if await self.collection_exists(collection_name, self.qdrant_url):
                    logger.info(f"Using fallback direct collection name: {collection_name}")
                    latest_collection_name = collection_name
                else:
                    logger.warning(f"No collection found for {collection_name}")
                    return None
            else:
                logger.info(f"Building versioned_collection_name using collection_name: {vector_db_instance.collection_name} and version: {vector_db_instance.version}")
                versioned_collection_name = f"{vector_db_instance.collection_name}.{vector_db_instance.version}"
                version_number = int(str(vector_db_instance.version)) if vector_db_instance.version is not None else None
                logger.info(f"Constructed versioned_collection_name: {versioned_collection_name}, version_number: {version_number}")

                # First check if the direct collection name exists (for manually created collections)
                if await self.collection_exists(collection_name, self.qdrant_url):
                    latest_collection_name = collection_name
                    logger.info(f"Using direct collection name: {collection_name}")
                else:
                    # Check if the versioned collection from database actually exists in Qdrant
                    if version_number is not None and version_number > 0:
                        if await self.collection_exists(versioned_collection_name, self.qdrant_url):
                            latest_collection_name = versioned_collection_name
                            logger.info(f"Using versioned collection name from database: {versioned_collection_name}")
                        else:
                            logger.warning(f"Database indicates version {version_number} but collection {versioned_collection_name} doesn't exist in Qdrant")
                            # Fallback to finding the actual latest collection
                            latest_collection_name = await self.get_latest_collection_name(collection_name, self.qdrant_url)
                    else:
                        latest_collection_name = await self.get_latest_collection_name(collection_name, self.qdrant_url)

                if latest_collection_name is None:
                    logger.warning(f"No latest collection name found for {collection_name}")
                    return None

        try:
            # Final safety check: ensure the collection we're about to search actually exists
            if not await self.collection_exists(latest_collection_name, self.qdrant_url):
                logger.error(f"Collection {latest_collection_name} does not exist in Qdrant, cannot perform search")
                return []

            # Generate query embedding
            query_embedding = self.embeddings.embed_query(query)

            # Search in Qdrant
            search_results = self.client.search(
                collection_name=latest_collection_name,
                query_vector=query_embedding,
                limit=n_results,
                with_payload=True
            )

            if not search_results:
                logger.warning(f"No relevant documents found for query in collection {collection_name}")
                return []

            # Extract documents and sort by chunk_index if available
            documents_with_metadata = []
            for result in search_results:
                payload = result.payload
                document = payload.get("document", "")
                chunk_index = payload.get("chunk_index", payload.get("document_index", 0))
                documents_with_metadata.append((chunk_index, document))

            # Sort by chunk_index and extract documents
            documents_with_metadata.sort(key=lambda x: x[0])
            documents_sorted = [doc for _, doc in documents_with_metadata]

            # Update version if needed (only if we have a database instance)
            if db is not None and 'vector_db_instance' in locals() and vector_db_instance and vector_db_instance.version is None and "." in latest_collection_name:
                last_part = latest_collection_name.split(".")[-1]
                if last_part.isdigit():
                    version = int(last_part)
                    await self.vector_db_service.update_version(db, unique_id, tenant_id, version)

            return documents_sorted

        except Exception as e:
            logger.error(f"Error searching in Qdrant collection {latest_collection_name}: {e}")
            return []
        
    async def ensure_chroma_instance_exists(self, db, unique_id, tenant_id, collection_name):
        """Ensure Qdrant instance mapping exists (reusing existing mapping table structure)"""
        try:
            vector_db_instance = await self.vector_db_service.get_by_unique_id_and_tenant(db, unique_id, tenant_id)
        except Exception as e:
            logger.warning(f"Database access failed, using fallback for collection {collection_name}: {e}")
            # Return None to trigger fallback logic in calling method
            return None

        if vector_db_instance is None:
            from models.kontratar_models import ChromaDBInstanceMapping
            vector_db_instance = ChromaDBInstanceMapping(
                unique_id=unique_id,
                tenant_id=tenant_id,
                chroma_instance_url=self.qdrant_url,
                collection_name=collection_name,
                created_date=datetime.utcnow(),
                status="ACTIVE"
            )
            db.add(vector_db_instance)
            await db.commit()
            await db.refresh(vector_db_instance)
            logger.info(f"Created new Qdrant mapping for {unique_id}, {tenant_id} with collection name {collection_name}")
        else:
            logger.info(f"Found existing mapping for {unique_id}, {tenant_id} with collection name {vector_db_instance.collection_name}")
    
        db_collection_name = str(vector_db_instance.collection_name)
    
        # Hash collection name if needed
        if db_collection_name == f"{tenant_id}_{unique_id}" or db_collection_name == unique_id:
            logger.info(f"Creating hash for collection name")
            hashed_collection_name = generate_hash(collection_name)
            logger.info(f"Generated new collection name: {hashed_collection_name} for unique_id: {unique_id}, tenant_id: {tenant_id}")
            
            # Clone existing collection if it exists
            if await self.collection_exists(db_collection_name, self.qdrant_url):
                clone_success = await self.clone_collection(self.qdrant_url, db_collection_name, hashed_collection_name, tenant_id, unique_id)
                if clone_success:
                    logger.info(f"Successfully cloned collection from {db_collection_name} to {hashed_collection_name}")
                else:
                    logger.warning(f"Failed to clone collection from {db_collection_name} to {hashed_collection_name}")
            else:
                logger.info(f"No existing collection to clone from: {db_collection_name}")

            # Update collection name with hash value
            await self.vector_db_service.update_by_unique_id_and_tenant(
                db, unique_id, tenant_id, collection_name=hashed_collection_name,
            )
            vector_db_instance.collection_name = hashed_collection_name

        return vector_db_instance

    async def clone_collection(self, chroma_url: str, old_collection_name: str, new_collection_name: str, tenant_id: str, unique_id: str) -> bool:
        """Clone all documents and embeddings from old collection to new collection in Qdrant"""
        logger.info(f"Cloning collection from {old_collection_name} to {new_collection_name}")
        
        try:
            # Get all points from old collection
            scroll_result = self.client.scroll(
                collection_name=old_collection_name,
                limit=10000,  # Adjust based on your needs
                with_payload=True,
                with_vectors=True
            )
            
            points, _ = scroll_result
            
            if not points:
                logger.warning(f"No points found in old collection {old_collection_name}")
                return False

            # Create new collection
            collection_metadata = {
                "tenant_id": tenant_id,
                "unique_id": unique_id
            }

            new_collection_id = await self.create_collection(chroma_url, new_collection_name, collection_metadata)
            if not new_collection_id:
                logger.error(f"Failed to create new collection {new_collection_name}")
                return False

            # Prepare points for new collection
            new_points = []
            for point in points:
                new_point = PointStruct(
                    id=str(uuid.uuid4()),  # Generate new ID
                    vector=point.vector,
                    payload=point.payload
                )
                new_points.append(new_point)

            # Upload points to new collection
            self.client.upsert(
                collection_name=new_collection_name,
                points=new_points
            )
            
            logger.info(f"Cloned {len(new_points)} points to new collection {new_collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error cloning collection from {old_collection_name} to {new_collection_name}: {e}")
            return False

    async def get_collection_id(self, chroma_url: str, collection_name: str) -> Optional[str]:
        """For Qdrant, collection name is the ID"""
        try:
            existing_collections = self.client.get_collections().collections
            if any(col.name == collection_name for col in existing_collections):
                return collection_name
            return None
        except Exception as e:
            logger.error(f"Error getting collection ID for {collection_name}: {e}")
            return None