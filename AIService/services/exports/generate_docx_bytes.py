"""
DOCX generation service for RFP draft export - returns bytes instead of saving to file
"""
import os
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Any, List, Optional, Dict
from io import BytesIO

from loguru import logger
from models.customer_models import AESTenant, CustomOppsTable, DataMetastore, Users
from services.exports.generat_docx import DocxGenerator
from services.exports.render_html import Html<PERSON><PERSON><PERSON>
from pdf2docx import Converter


class DocxBytesGenerator:
    """Service for generating DOCX documents as bytes from markdown content"""

    @staticmethod
    def generate_docx_bytes(
        markdown_content: str,
        opportunity_id: str,
        tenant_id: str,
        cover_page: Optional[DataMetastore] = None,
        tenant_details: Optional[AESTenant] = None,
        opportunity_details: Optional[CustomOppsTable] = None,
        user_details: Optional[Users] = None,
        toc_data: Optional[List[Dict[str, Any]]] = None,
        trailing_page_markdown: Optional[str] = None,
        compliance: Optional[dict] = None,
        volume_number: int = 1,
        document_title: Optional[str] = None
    ) -> tuple[bytes, str]:
        """
        Generate DOCX from markdown content by first generating a PDF, then converting PDF to DOCX.
        """
        import tempfile
        import os

        try:
            logger.info(f"Starting DOCX bytes generation via PDF for opportunity {opportunity_id}, volume {volume_number}")
            
            
            # --- block to prepare cover_page_elements ---
            cover_page_elements = None
            if cover_page:
                from services.exports.generate_pdf_bytes import PDFGenerator
                cover_page_elements = PDFGenerator.create_cover_page_elements(
                    cover_page=cover_page,
                    tenant_details=tenant_details,
                    opportunity_details=opportunity_details,
                    user_details=user_details,
                    compliance=compliance,
                    image_only=True
                )
            # -----------------------------------------------------

            # 1. Generate PDF bytes using your existing PDF generator
            from services.exports.generate_pdf_bytes import PDFGenerator
            # Extract opportunity title for header - mandatory
            opportunity_title = "Opportunity Title Not Available"
            if opportunity_details and hasattr(opportunity_details, 'title') and opportunity_details.title:
                opportunity_title = opportunity_details.title
            else:
                logger.warning(f"No opportunity title found for opportunity_id={opportunity_id}, using default")

            pdf_bytes, pdf_message = PDFGenerator.generate_pdf(
                markdown_content=markdown_content,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                cover_page_elements=cover_page_elements,  # Add cover page logic if needed
                toc_data=toc_data,
                trailing_page_markdown=trailing_page_markdown,
                compliance=compliance,
                volume_number=volume_number,
                image_only=True,
                opportunity_title=opportunity_title
            )

            # 2. Write PDF bytes to a temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as pdf_file:
                pdf_file.write(pdf_bytes)
                pdf_path = pdf_file.name

            # 3. Prepare a temporary file for the DOCX output
            with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as docx_file:
                docx_path = docx_file.name

            # 4. Convert PDF to DOCX using pdf2docx
            cv = Converter(pdf_path)
            cv.convert(docx_path, start=0, end=None)
            cv.close()

            # 5. Read the DOCX file as bytes
            with open(docx_path, 'rb') as f:
                docx_bytes = f.read()

            # 6. Clean up temp files
            os.unlink(pdf_path)
            os.unlink(docx_path)

            logger.info(f"DOCX successfully generated from PDF ({len(docx_bytes)} bytes)")
            return docx_bytes, f"RFP Draft DOCX successfully generated from PDF for {opportunity_id}"

        except Exception as e:
            logger.error(f"Error generating DOCX via PDF: {e}")
            return b'', f"Failed to generate DOCX via PDF: {str(e)}"

    @staticmethod
    def generate_docx_bytes_with_image_only_cover(
        markdown_content: str,
        opportunity_id: str,
        tenant_id: str,
        cover_page: Optional[DataMetastore],
        tenant_details: Optional[AESTenant],
        opportunity_details: Optional[CustomOppsTable],
        user_details: Optional[Users],
        toc_data: Optional[List[Dict[str, Any]]] = None,
        trailing_page_markdown: Optional[str] = None,
        compliance: Optional[dict] = None,
        volume_number: int = 1,
        document_title: Optional[str] = None
    ) -> tuple[bytes, str]:
        """
        Generate DOCX with image-only cover page (no text overlay) and return as bytes.
        
        Args:
            markdown_content: The main content to convert to DOCX
            opportunity_id: The opportunity identifier
            tenant_id: The tenant identifier
            cover_page: The cover page document
            tenant_details: Tenant information
            opportunity_details: Opportunity information
            user_details: User information
            toc_data: Optional table of contents data
            trailing_page_markdown: Optional trailing page content
            compliance: Optional compliance formatting settings
            volume_number: Volume number (1-5) to determine header title
            document_title: Optional document title
            
        Returns:
            tuple[bytes, str]: (docx_bytes, success_message)
        """
        try:
            logger.info(f"Starting DOCX bytes generation with image-only cover for opportunity {opportunity_id}, volume {volume_number}")
            
            # Set default compliance settings if not provided
            if not compliance:
                compliance = {
                    'font_type': 'Times New Roman',
                    'font_size_body': 12,
                    'font_size_header': 14,
                    'font_size_footer': 10,
                    'line_spacing': 1.5,
                    'margin': 50
                }
            
            # Convert markdown to HTML first
            html_content = HtmlRenderer.render_markdown_to_html(
                markdown_content=markdown_content,
                font_type=compliance.get('font_type', 'Times-Roman'),
                font_size_body=compliance.get('font_size_body', 12),
                font_size_header=compliance.get('font_size_header', 14),
                font_size_title=compliance.get('font_size_title', 18),
                font_size_subheading=compliance.get('font_size_subheading', 12),
                line_spacing=compliance.get('line_spacing', 1.5)
            )
            
            # Prepare cover page elements with image-only mode
            cover_page_elements = {
                'cover_page': cover_page,
                'tenant_details': tenant_details,
                'opportunity_details': opportunity_details,
                'user_details': user_details,
                'compliance': compliance,
                'image_only': True  # Force image-only mode
            }
            
            # Generate filename for temporary file
            filename = f"RFP_Draft_Vol_{volume_number}_{opportunity_id}_ImageOnly.docx"
            
            # Create temporary file to generate DOCX
            with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
                temp_path = temp_file.name
            
            try:
                # Generate DOCX file using existing DocxGenerator
                output_path = DocxGenerator.generate_docx_from_html(
                    html_content=html_content,
                    output_file_path=temp_path,
                    metadata={'title': document_title or f'RFP Draft Volume {volume_number}'},
                    css_paths=None,
                    inline_css=True,
                    enforce_table_grid=True,
                    header_title=f"Volume {volume_number}",
                    include_toc=bool(toc_data),
                    toc_title="Table of Contents",
                    toc_levels="1-3",
                    toc_data=toc_data,
                    cover_page=cover_page,
                    cover_page_elements=cover_page_elements
                )
                
                # Read the generated file as bytes
                with open(output_path, 'rb') as docx_file:
                    docx_bytes = docx_file.read()
                
                logger.info(f"DOCX with image-only cover successfully generated as bytes ({len(docx_bytes)} bytes)")
                return docx_bytes, f"RFP Draft DOCX with image-only cover successfully generated ({filename})"
                
            finally:
                # Clean up temporary file
                try:
                    if os.path.exists(temp_path):
                        os.unlink(temp_path)
                except Exception as cleanup_error:
                    logger.warning(f"Failed to clean up temporary file {temp_path}: {cleanup_error}")
                    
        except Exception as e:
            logger.error(f"Error generating DOCX bytes with image-only cover: {e}")
            # Return empty bytes and error message
            return b'', f"Failed to generate DOCX with image-only cover: {str(e)}"


__all__ = ["DocxBytesGenerator"]
