import asyncio
import os
import json
import shutil
from datetime import datetime
from typing import Optional
from controllers.customer.tenant_controller import TenantController
from services.proposal.utilities import ProposalUtilities
from services.proposal.outline import ProposalOutlineService
from services.proposal.content_compliance import ContentComplianceService
from services.proposal.structure_compliance import StructureComplianceService
from controllers.customer.rfp_draft_export_controller import RfpDraftExportController
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from database import get_customer_db


def create_dedicated_folder(opportunity_id: str) -> str:
    """Create a dedicated folder for all generated content"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    folder_name = f"proposal_generation_{opportunity_id}_{timestamp}"
    folder_path = os.path.join("generated-proposals", folder_name)

    # Create the directory structure
    os.makedirs(folder_path, exist_ok=True)
    os.makedirs(os.path.join(folder_path, "outlines"), exist_ok=True)
    os.makedirs(os.path.join(folder_path, "drafts"), exist_ok=True)
    os.makedirs(os.path.join(folder_path, "pdfs"), exist_ok=True)
    os.makedirs(os.path.join(folder_path, "docx"), exist_ok=True)
    os.makedirs(os.path.join(folder_path, "compliances"), exist_ok=True)

    print(f"Created dedicated folder: {folder_path}")
    return folder_path


def save_draft_to_json(draft_data: dict, volume_number: int, folder_path: str, opportunity_id: str) -> str:
    """Save draft data to JSON file in dedicated folder"""
    filename = f"draft_volume_{volume_number}_{opportunity_id}.json"
    filepath = os.path.join(folder_path, "drafts", filename)

    with open(filepath, 'w') as f:
        json.dump(draft_data, f, indent=2)

    print(f"Saved draft for volume {volume_number} to {filepath}")
    return filepath


def save_outline_to_json(outline_data: dict, volume_number: int, folder_path: str, opportunity_id: str) -> str:
    """Save outline data to JSON file in dedicated folder"""
    filename = f"outline_volume_{volume_number}_{opportunity_id}.json"
    filepath = os.path.join(folder_path, "outlines", filename)

    with open(filepath, 'w') as f:
        json.dump(outline_data, f, indent=2)

    print(f"Saved outline for volume {volume_number} to {filepath}")
    return filepath


def save_compliance_to_files(content_compliance: str, structure_compliance: str, folder_path: str, opportunity_id: str) -> tuple[str, str]:
    """Save compliance data to files in dedicated folder"""
    # Create compliance directory
    compliance_dir = os.path.join(folder_path, "compliances")
    os.makedirs(compliance_dir, exist_ok=True)

    # Save content compliance
    content_filename = f"content_compliance_{opportunity_id}.txt"
    content_filepath = os.path.join(compliance_dir, content_filename)
    with open(content_filepath, 'w') as f:
        f.write(content_compliance)

    # Save structure compliance
    structure_filename = f"structure_compliance_{opportunity_id}.txt"
    structure_filepath = os.path.join(compliance_dir, structure_filename)
    with open(structure_filepath, 'w') as f:
        f.write(structure_compliance)

    print(f"Saved content compliance to {content_filepath}")
    print(f"Saved structure compliance to {structure_filepath}")

    return content_filepath, structure_filepath


def save_toc_to_json(toc_result: dict, volume_toc: list, volume_number: int, folder_path: str, opportunity_id: str, volume_title: str) -> str:
    """Save table of contents result to JSON file in dedicated folder"""
    # Create TOCs directory
    toc_dir = os.path.join(folder_path, "tocs")
    os.makedirs(toc_dir, exist_ok=True)

    toc_json = {
        "opportunity_id": opportunity_id,
        "volume_number": volume_number,
        "volume_title": volume_title,
        "toc_raw_content": toc_result.get("content", ""),
        "table_of_contents": volume_toc,
        "section_count": len(volume_toc),
        "generated_at": datetime.now().isoformat()
    }

    filename = f"toc_volume_{volume_number}_{opportunity_id}.json"
    filepath = os.path.join(toc_dir, filename)

    with open(filepath, 'w') as f:
        json.dump(toc_json, f, indent=2)

    print(f"Saved TOC for volume {volume_number} to {filepath}")
    return filepath


async def export_outline_to_pdf_using_controller(
    outline_data: dict,
    volume_number: int,
    folder_path: str,
    opportunity_id: str,
    tenant_id: str,
    volume_toc: Optional[list] = None
) -> str:
    """Export outline to PDF using new markdown-based outline structure"""
    try:
        # Check if this is the new markdown-based outline structure
        if "outlines" in outline_data and outline_data["outlines"]:
            first_outline = outline_data["outlines"][0]
            if "markdown" in first_outline:
                # New structure - process markdown outlines
                return await export_outline_markdown_to_pdf(
                    outline_data, volume_number, folder_path, opportunity_id, tenant_id, volume_toc
                )

        # Fallback to old structure for backward compatibility
        return await export_outline_legacy_to_pdf(
            outline_data, volume_number, folder_path, opportunity_id, tenant_id, volume_toc
        )

    except Exception as e:
        print(f"Error exporting outline to PDF: {e}")
        return ""


async def export_outline_markdown_to_pdf(
    outline_data: dict,
    volume_number: int,
    folder_path: str,
    opportunity_id: str,
    tenant_id: str,
    volume_toc: Optional[list] = None
) -> str:
    """Export new markdown-based outline to PDF with proper formatting and references"""
    try:
# Debug prints removed - function working correctly

        # Create target path for outline PDF
        pdf_filename = f"outline_volume_{volume_number}_{opportunity_id}.pdf"
        target_path = os.path.join(folder_path, "pdfs", pdf_filename)
        os.makedirs(os.path.dirname(target_path), exist_ok=True)

        # Create target path for references PDF
        references_filename = f"references_volume_{volume_number}_{opportunity_id}.pdf"
        references_path = os.path.join(folder_path, "pdfs", references_filename)

        # Get TOC numbering for proper section numbering
        toc_numbering = {}
        if volume_toc:
            for toc_item in volume_toc:
                title = toc_item.get("title", "")
                number = toc_item.get("number", "")
                if title and number:
                    toc_numbering[title] = number

        # Collect references for separate PDF
        all_references = []

        if "outlines" in outline_data and outline_data["outlines"]:
            for section in outline_data["outlines"]:
                title = section.get("title", "")
                references = section.get("references", [])

                # Get section number from TOC or use default
                section_number = toc_numbering.get(title, "")

                # Collect references for separate PDF
                if references:
                    section_refs = {
                        "section_number": section_number,
                        "section_title": title,
                        "references": references
                    }
                    all_references.append(section_refs)

        # Convert outline data to draft format for the controller (like legacy function)
        # Let MarkdownRenderer handle ALL formatting - just provide clean raw content
        draft_list = []
        for i, section in enumerate(outline_data["outlines"], 1):
            title = section.get("title", f"Section {i}")
            markdown_content = section.get("markdown", "")

            # Get section number from TOC for proper header numbering
            section_number = toc_numbering.get(title, "")
            numbered_title = f"{section_number} {title}" if section_number else title

            # Only do minimal cleaning - let MarkdownRenderer handle all formatting
            cleaned_content = clean_outline_content_for_draft(markdown_content, title)

            draft_list.append({
                "title": numbered_title,  # Include section number in title
                "content": cleaned_content
            })

        # Prepare update fields with draft and correct TOC field for this volume (like legacy function)
        toc_field_name = f"toc_text" if volume_number == 1 else f"toc_text_{volume_number}"
        update_fields = {
            "draft": json.dumps(draft_list)
        }

        # Add the correct TOC for this volume if provided (like legacy function)
        if volume_toc:
            update_fields[toc_field_name] = json.dumps(volume_toc)
            print(f"Temporarily updating {toc_field_name} with {len(volume_toc)} TOC sections for volume {volume_number}")

        # Use the same database session for both record creation and export
        user_id = 69  # As requested
        version = 1
        file_path = ""

        async for db in get_customer_db():
            # Update/create the database record with this outline as draft and correct TOC
            update_result = await CustomOpportunitiesController.update_by_opportunity_id(db, opportunity_id, update_fields)
            if not update_result:
                # If update returns None, create a new record
                from models.customer_models import CustomOppsTable
                new_record = CustomOppsTable(
                    opportunity_id=opportunity_id,
                    tenant_id=tenant_id,
                    title=f"Outline - Volume {volume_number}",
                    description="Proposal outline",
                    **update_fields
                )
                db.add(new_record)
                await db.commit()
                await db.refresh(new_record)

            # Generate PDF using the same database session
            file_path, _ = await RfpDraftExportController.export_rfp_draft(
                db=db,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                user_id=user_id,
                version=version,
                cover_page_id=15217,
                trailing_page_id=None,
                volume_number=volume_number
            )
            break

        # Move the generated PDF to our dedicated folder structure (like legacy function)
        if file_path and os.path.exists(file_path):
            # Create target path in our dedicated folder
            pdf_filename = f"outline_volume_{volume_number}_{opportunity_id}.pdf"
            target_path = os.path.join(folder_path, "pdfs", pdf_filename)

            # Create the PDF directory if it doesn't exist
            os.makedirs(os.path.dirname(target_path), exist_ok=True)

            # Move the file to our dedicated folder
            shutil.move(file_path, target_path)

            print(f"Exported outline for volume {volume_number} to PDF: {target_path}")
        else:
            print(f"Error: Outline PDF generation failed for volume {volume_number}")
            target_path = ""

        # Generate references PDF if there are references using RfpDraftExportController
        if all_references:
            # Create references draft in the same format as main content
            references_draft_list = []
            for section_refs in all_references:
                section_number = section_refs.get("section_number", "")
                section_title = section_refs.get("section_title", "")
                references = section_refs.get("references", [])

                if references:
                    # Create content for this section's references - use minimal cleaning like main content
                    ref_content_lines = []
                    for i, reference in enumerate(references, 1):
                        cleaned_ref = reference.strip().replace('"', '').replace("'", "'")
                        ref_content_lines.append(f"{i}. {cleaned_ref}")

                    # Create simple markdown content for references
                    ref_content = "\n\n".join(ref_content_lines)

                    references_draft_list.append({
                        "title": f"{section_number} {section_title} - References" if section_number else f"{section_title} - References",
                        "content": ref_content
                    })

            if references_draft_list:
                # Create a temporary opportunity ID for references
                references_opportunity_id = f"{opportunity_id}_references_vol_{volume_number}"

                # Prepare update fields for references
                references_update_fields = {
                    "draft": json.dumps(references_draft_list)
                }

                # Create a simple TOC for references
                references_toc = [{
                    "title": f"References - Volume {volume_number}",
                    "description": "References cited in the proposal outline",
                    "number": "1.0",
                    "page_limit": len(references_draft_list) * 2,
                    "subsections": []
                }]

                references_update_fields["toc_text"] = json.dumps(references_toc)

                # Update database with references draft
                try:
                    async for db in get_customer_db():
                        # Try to update first
                        update_result = await CustomOpportunitiesController.update_by_opportunity_id(db, references_opportunity_id, references_update_fields)
                        if not update_result:
                            # If update returns None, create a new record
                            from models.customer_models import CustomOppsTable
                            new_record = CustomOppsTable(
                                opportunity_id=references_opportunity_id,
                                tenant_id=tenant_id,
                                title=f"References - Volume {volume_number}",
                                description="References for proposal outline",
                                **references_update_fields
                            )
                            db.add(new_record)
                            await db.commit()
                            await db.refresh(new_record)
                        break
                except Exception as e:
                    print(f"❌ Error with database operations for references: {e}")
                    return target_path

                # Generate references PDF using RfpDraftExportController
                references_file_path = ""
                try:
                    async for db in get_customer_db():
                        references_file_path, _ = await RfpDraftExportController.export_rfp_draft(
                            db=db,
                            opportunity_id=references_opportunity_id,
                            tenant_id=tenant_id,
                            user_id=69,
                            version=1,
                            cover_page_id=15217,  # Same cover page as main content
                            trailing_page_id=None,
                            volume_number=volume_number
                        )
                        break
                except Exception as e:
                    print(f"❌ Error generating references PDF: {e}")
                    import traceback
                    traceback.print_exc()

                # Move to target location
                if references_file_path and os.path.exists(references_file_path):
                    shutil.move(references_file_path, references_path)
                    print(f"Exported references for volume {volume_number} to PDF: {references_path}")
                else:
                    print(f"Error: References PDF generation failed for volume {volume_number}")

        return target_path

    except Exception as e:
        print(f"Error exporting markdown outline to PDF: {e}")
        return ""


async def export_outline_legacy_to_pdf(
    outline_data: dict,
    volume_number: int,
    folder_path: str,
    opportunity_id: str,
    tenant_id: str,
    volume_toc: Optional[list] = None
) -> str:
    """Export legacy outline format to PDF (backward compatibility)"""
    try:
        # Convert outline data to draft format for the controller
        if "outlines" in outline_data:
            draft_list = []
            for i, section in enumerate(outline_data["outlines"], 1):
                draft_list.append({
                    "title": section.get("title", f"Section {i}"),
                    "content": section.get("content", "No content available")
                })
        else:
            draft_list = []

        # Prepare update fields with draft and correct TOC field for this volume
        toc_field_name = f"toc_text" if volume_number == 1 else f"toc_text_{volume_number}"
        update_fields = {
            "draft": json.dumps(draft_list)
        }

        # Add the correct TOC for this volume if provided
        if volume_toc:
            update_fields[toc_field_name] = json.dumps(volume_toc)
            print(f"Temporarily updating {toc_field_name} with {len(volume_toc)} TOC sections for volume {volume_number}")

        # Temporarily update the database with this outline as draft and correct TOC
        async for db in get_customer_db():
            await CustomOpportunitiesController.update_by_opportunity_id(db, opportunity_id, update_fields)
            break

        # Use the RfpDraftExportController to generate PDF
        user_id = 69  # As requested
        version = 1
        file_path = ""

        async for db in get_customer_db():
            file_path, _ = await RfpDraftExportController.export_rfp_draft(
                db=db,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                user_id=user_id,
                version=version,
                cover_page_id=15217,
                trailing_page_id=None,
                volume_number=volume_number
            )
            break

        # Move the generated PDF to our dedicated folder structure
        if file_path and os.path.exists(file_path):
            # Create target path in our dedicated folder
            pdf_filename = f"outline_volume_{volume_number}_{opportunity_id}.pdf"
            target_path = os.path.join(folder_path, "pdfs", pdf_filename)

            # Create the PDF directory if it doesn't exist
            os.makedirs(os.path.dirname(target_path), exist_ok=True)

            # Move the file to our dedicated folder
            shutil.move(file_path, target_path)

            print(f"Exported outline for volume {volume_number} to PDF: {target_path}")
            return target_path
        else:
            print(f"Error: Outline PDF generation failed for volume {volume_number}")
            return ""

    except Exception as e:
        print(f"Error exporting legacy outline to PDF: {e}")
        return ""


async def export_outline_to_docx_using_controller(
    outline_data: dict,
    volume_number: int,
    folder_path: str,
    opportunity_id: str,
    tenant_id: str,
    volume_toc: Optional[list] = None
) -> str:
    """Export outline to DOCX using new markdown-based outline structure"""
    try:
        # Check if this is the new markdown-based outline structure
        if "outlines" in outline_data and outline_data["outlines"]:
            first_outline = outline_data["outlines"][0]
            if "markdown" in first_outline:
                # New structure - process markdown outlines
                return await export_outline_markdown_to_docx(
                    outline_data, volume_number, folder_path, opportunity_id, tenant_id, volume_toc
                )

        # Fallback to old structure for backward compatibility
        return await export_outline_legacy_to_docx(
            outline_data, volume_number, folder_path, opportunity_id, tenant_id, volume_toc
        )

    except Exception as e:
        print(f"Error exporting outline to DOCX: {e}")
        return ""


async def export_outline_markdown_to_docx(
    outline_data: dict,
    volume_number: int,
    folder_path: str,
    opportunity_id: str,
    tenant_id: str,
    volume_toc: Optional[list] = None
) -> str:
    """Export new markdown-based outline to DOCX with proper formatting and references"""
    try:
        # Get TOC numbering for proper section numbering
        toc_numbering = {}
        if volume_toc:
            for item in volume_toc:
                title = item.get("title", "")
                number = item.get("number", "")
                if title and number:
                    toc_numbering[title] = number

        # Collect references for separate DOCX
        all_references = []

        if "outlines" in outline_data and outline_data["outlines"]:
            for section in outline_data["outlines"]:
                title = section.get("title", "")
                references = section.get("references", [])

                # Get section number from TOC or use default
                section_number = toc_numbering.get(title, "")

                # Collect references for separate DOCX
                if references:
                    section_refs = {
                        "section_number": section_number,
                        "section_title": title,
                        "references": references
                    }
                    all_references.append(section_refs)

        # Convert outline data to draft format for the controller (like legacy function)
        # Let MarkdownRenderer handle ALL formatting - just provide clean raw content
        draft_list = []
        for i, section in enumerate(outline_data["outlines"], 1):
            title = section.get("title", f"Section {i}")
            markdown_content = section.get("markdown", "")

            # Get section number from TOC for proper header numbering
            section_number = toc_numbering.get(title, "")
            numbered_title = f"{section_number} {title}" if section_number else title

            # Only do minimal cleaning - let MarkdownRenderer handle all formatting
            cleaned_content = clean_outline_content_for_draft(markdown_content, title)

            draft_list.append({
                "title": numbered_title,  # Include section number in title
                "content": cleaned_content
            })

        # Prepare update fields with draft and correct TOC field for this volume
        toc_field_name = f"toc_text" if volume_number == 1 else f"toc_text_{volume_number}"
        update_fields = {
            "draft": json.dumps(draft_list)
        }

        # Add the correct TOC for this volume if provided
        if volume_toc:
            update_fields[toc_field_name] = json.dumps(volume_toc)

        # Temporarily update the database with this volume's draft and correct TOC
        async for db in get_customer_db():
            await CustomOpportunitiesController.update_by_opportunity_id(db, opportunity_id, update_fields)
            break

        # Use the RfpDraftExportController to generate DOCX
        user_id = 69  # As requested
        version = 1
        file_path = ""

        async for db in get_customer_db():
            file_path, _ = await RfpDraftExportController.export_rfp_draft_docx(
                db=db,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                user_id=user_id,
                version=version,
                cover_page_id=15217,
                trailing_page_id=None,
                volume_number=volume_number,
                include_toc=True,
                toc_title="Table of Contents",
                toc_levels="1-2"
            )
            break

        # Move the generated DOCX to our dedicated folder structure
        if file_path and os.path.exists(file_path):
            # Create target path in our dedicated folder
            docx_filename = f"outline_volume_{volume_number}_{opportunity_id}.docx"
            target_path = os.path.join(folder_path, "docx", docx_filename)

            # Create the DOCX directory if it doesn't exist
            os.makedirs(os.path.dirname(target_path), exist_ok=True)

            # Move the file to our dedicated folder
            shutil.move(file_path, target_path)

            print(f"Exported outline for volume {volume_number} to DOCX: {target_path}")
        else:
            print(f"Error: Outline DOCX generation failed for volume {volume_number}")
            target_path = ""

        # Generate references DOCX if there are references using RfpDraftExportController
        if all_references:
            # Create references draft in the same format as main content
            references_draft_list = []
            for section_refs in all_references:
                section_number = section_refs.get("section_number", "")
                section_title = section_refs.get("section_title", "")
                references = section_refs.get("references", [])

                if references:
                    # Create content for this section's references
                    content = ""
                    for i, reference in enumerate(references, 1):
                        cleaned_ref = reference.strip().replace('"', '').replace("'", "'")
                        content += f"{i}. {cleaned_ref}\n\n"

                    references_draft_list.append({
                        "title": f"{section_number} {section_title}",
                        "content": content.strip()
                    })

            if references_draft_list:
                # Create a unique opportunity ID for references
                references_opportunity_id = f"{opportunity_id}_references_vol_{volume_number}"
                references_path = os.path.join(folder_path, "docx", f"references_volume_{volume_number}_{opportunity_id}.docx")

                # Update database with references draft
                references_update_fields = {
                    "draft": json.dumps(references_draft_list)
                }

                try:
                    async for db in get_customer_db():
                        await CustomOpportunitiesController.update_by_opportunity_id(db, references_opportunity_id, references_update_fields)
                        break
                except Exception as e:
                    print(f"❌ Error with database operations for references: {e}")
                    return target_path

                # Generate references DOCX using RfpDraftExportController
                references_file_path = ""
                try:
                    async for db in get_customer_db():
                        references_file_path, _ = await RfpDraftExportController.export_rfp_draft_docx(
                            db=db,
                            opportunity_id=references_opportunity_id,
                            tenant_id=tenant_id,
                            user_id=69,
                            version=1,
                            cover_page_id=15217,  # Same cover page as main content
                            trailing_page_id=None,
                            volume_number=volume_number,
                            include_toc=True,
                            toc_title="References",
                            toc_levels="1-2"
                        )
                        break
                except Exception as e:
                    print(f"❌ Error generating references DOCX: {e}")
                    import traceback
                    traceback.print_exc()

                # Move to target location
                if references_file_path and os.path.exists(references_file_path):
                    shutil.move(references_file_path, references_path)
                    print(f"Exported references for volume {volume_number} to DOCX: {references_path}")
                else:
                    print(f"Error: References DOCX generation failed for volume {volume_number}")

        return target_path

    except Exception as e:
        print(f"Error exporting markdown outline to DOCX: {e}")
        return ""


async def export_outline_legacy_to_docx(
    outline_data: dict,
    volume_number: int,
    folder_path: str,
    opportunity_id: str,
    tenant_id: str,
    volume_toc: Optional[list] = None
) -> str:
    """Export legacy outline format to DOCX (backward compatibility)"""
    try:
        # Convert outline data to draft format for the controller
        if "outlines" in outline_data:
            draft_list = []
            for i, section in enumerate(outline_data["outlines"], 1):
                draft_list.append({
                    "title": section.get("title", f"Section {i}"),
                    "content": section.get("content", "No content available")
                })
        else:
            draft_list = []

        # Prepare update fields
        update_fields = {
            "draft": json.dumps(draft_list)
        }

        # Add TOC data if provided
        if volume_toc:
            toc_field_name = f"toc_text" if volume_number == 1 else f"toc_text_{volume_number}"
            update_fields[toc_field_name] = json.dumps(volume_toc)

        # Temporarily update the database
        async for db in get_customer_db():
            await CustomOpportunitiesController.update_by_opportunity_id(db, opportunity_id, update_fields)
            break

        # Use the RfpDraftExportController to generate DOCX
        user_id = 69
        version = 1
        file_path = ""

        async for db in get_customer_db():
            file_path, _ = await RfpDraftExportController.export_rfp_draft_docx(
                db=db,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                user_id=user_id,
                version=version,
                cover_page_id=15217,
                trailing_page_id=None,
                volume_number=volume_number,
                include_toc=True,
                toc_title="Table of Contents",
                toc_levels="1-2"
            )
            break

        # Move the generated DOCX to our dedicated folder structure
        if file_path and os.path.exists(file_path):
            # Create target path in our dedicated folder
            docx_filename = f"outline_volume_{volume_number}_{opportunity_id}.docx"
            target_path = os.path.join(folder_path, "docx", docx_filename)

            # Create the DOCX directory if it doesn't exist
            os.makedirs(os.path.dirname(target_path), exist_ok=True)

            # Move the file to our dedicated folder
            shutil.move(file_path, target_path)

            print(f"Exported outline for volume {volume_number} to DOCX: {target_path}")
            return target_path
        else:
            print(f"Error: Outline DOCX generation failed for volume {volume_number}")
            return ""

    except Exception as e:
        print(f"Error exporting legacy outline to DOCX: {e}")
        return ""


def clean_outline_content_for_draft(markdown_content: str, title: str) -> str:
    """Minimal cleaning of outline content - let MarkdownRenderer handle all formatting"""
    try:
        lines = markdown_content.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()

            # Skip empty lines
            if not line:
                cleaned_lines.append('')
                continue

            # Skip the main title line (MarkdownRenderer adds ## {title} automatically)
            if line.startswith(f"## {title}") or line.startswith(f"# {title}"):
                continue

            # Skip page limit lines
            if "(Page limit:" in line and "pages)" in line:
                continue

            # Remove quotes from content but keep the structure
            cleaned_line = line.replace('"', '').replace("'", "'")
            cleaned_lines.append(cleaned_line)

        # Remove references section completely
        result_lines = []
        in_references = False

        for line in cleaned_lines:
            if line.strip().startswith('**References:**'):
                in_references = True
                continue

            if in_references:
                # Check if we've moved to a new section
                if line.strip().startswith('**') and not line.strip().startswith('**References:**'):
                    in_references = False
                    result_lines.append(line)
                # Skip reference lines
                continue
            else:
                result_lines.append(line)

        return '\n'.join(result_lines).strip()

    except Exception as e:
        print(f"Error cleaning outline content: {e}")
        return markdown_content


# Old functions removed - now using minimal cleaning approach



def create_references_markdown(all_references: list, volume_number: int) -> str:
    """Create a formatted markdown document for all references"""
    try:
        markdown_content = f"# References - Volume {volume_number}\n\n"
        markdown_content += "This appendix contains all references cited in the proposal outline.\n\n"

        for section_refs in all_references:
            section_number = section_refs.get("section_number", "")
            section_title = section_refs.get("section_title", "")
            references = section_refs.get("references", [])

            if references:
                markdown_content += f"## {section_number} {section_title}\n\n"

                for i, reference in enumerate(references, 1):
                    # Clean up the reference text
                    cleaned_ref = reference.strip().replace('"', '').replace("'", "'")
                    markdown_content += f"{i}. {cleaned_ref}\n\n"

                markdown_content += "---\n\n"

        return markdown_content

    except Exception as e:
        print(f"Error creating references markdown: {e}")
        return f"# References - Volume {volume_number}\n\nError generating references."


async def export_draft_to_pdf_using_controller(
    draft_data: dict,
    volume_number: int,
    folder_path: str,
    opportunity_id: str,
    tenant_id: str,
    volume_toc: Optional[list] = None
) -> str:
    """Export draft to PDF using RfpDraftExportController exactly like the export endpoint"""
    try:
        # First, we need to temporarily save the draft data to the database
        # so the controller can read it (since it reads from CustomOppsTable.draft)

        # Save the draft data to a temporary database entry or use the existing one
        # For now, we'll create a temporary JSON file that mimics the database structure
        temp_draft_file = os.path.join(folder_path, "drafts", f"temp_draft_vol_{volume_number}.json")

        # Convert draft_data to the format expected by the controller
        if "draft" in draft_data:
            draft_list = draft_data["draft"]
        else:
            draft_list = []

        # Save temporary draft file
        with open(temp_draft_file, 'w') as f:
            json.dump(draft_list, f, indent=2)

        # Prepare update fields with draft and correct TOC field for this volume
        toc_field_name = f"toc_text" if volume_number == 1 else f"toc_text_{volume_number}"
        update_fields = {
            "draft": json.dumps(draft_list)
        }

        # Add the correct TOC for this volume if provided
        if volume_toc:
            update_fields[toc_field_name] = json.dumps(volume_toc)
            print(f"Temporarily updating {toc_field_name} with {len(volume_toc)} TOC sections for volume {volume_number}")

        # Temporarily update the database with this volume's draft and correct TOC
        async for db in get_customer_db():
            await CustomOpportunitiesController.update_by_opportunity_id(db, opportunity_id, update_fields)
            break

        # Use the RfpDraftExportController to generate PDF exactly like the export endpoint
        user_id = 69  # As requested
        version = 1
        file_path = ""

        async for db in get_customer_db():
            file_path, _ = await RfpDraftExportController.export_rfp_draft(
                db=db,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                user_id=user_id,
                version=version,
                cover_page_id=15217,  # No cover page for individual volumes
                trailing_page_id=None,  # No trailing page for individual volumes
                volume_number=volume_number
            )
            break

        # Move the generated PDF to our dedicated folder structure
        if file_path and os.path.exists(file_path):
            # Create target path in our dedicated folder
            pdf_filename = f"draft_volume_{volume_number}_{opportunity_id}.pdf"
            target_path = os.path.join(folder_path, "pdfs", pdf_filename)

            # Create the PDF directory if it doesn't exist
            os.makedirs(os.path.dirname(target_path), exist_ok=True)

            # Move the file to our dedicated folder
            shutil.move(file_path, target_path)

            print(f"Exported draft for volume {volume_number} to PDF using controller: {target_path}")
            return target_path
        else:
            print(f"Error: PDF generation failed for volume {volume_number}")
            return ""

    except Exception as e:
        print(f"Error exporting draft to PDF using controller: {e}")
        return ""


async def export_draft_to_docx_using_controller(
    draft_data: dict,
    volume_number: int,
    folder_path: str,
    opportunity_id: str,
    tenant_id: str,
    volume_toc: Optional[list] = None
) -> str:
    """Export draft to DOCX using RfpDraftExportController"""
    try:
        # First, we need to temporarily save the draft data to the database
        # so the controller can read it (since it reads from CustomOppsTable.draft)

        # Save the draft data to a temporary database entry or use the existing one
        # For now, we'll create a temporary JSON file that mimics the database structure
        temp_draft_file = os.path.join(folder_path, "drafts", f"temp_draft_vol_{volume_number}.json")

        # Convert draft_data to the format expected by the controller
        if "draft" in draft_data:
            draft_list = draft_data["draft"]
        else:
            draft_list = []

        # Save temporary draft file
        with open(temp_draft_file, 'w') as f:
            json.dump(draft_list, f, indent=2)

        # Prepare update fields with draft and correct TOC field for this volume
        toc_field_name = f"toc_text" if volume_number == 1 else f"toc_text_{volume_number}"
        update_fields = {
            "draft": json.dumps(draft_list)
        }

        # Add the correct TOC for this volume if provided
        if volume_toc:
            update_fields[toc_field_name] = json.dumps(volume_toc)
            print(f"Temporarily updating {toc_field_name} with {len(volume_toc)} TOC sections for volume {volume_number}")

        # Temporarily update the database with this volume's draft and correct TOC
        async for db in get_customer_db():
            await CustomOpportunitiesController.update_by_opportunity_id(db, opportunity_id, update_fields)
            break

        # Use the RfpDraftExportController to generate DOCX exactly like the export endpoint
        user_id = 69  # As requested
        version = 1
        file_path = ""

        async for db in get_customer_db():
            file_path, _ = await RfpDraftExportController.export_rfp_draft_docx(
                db=db,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                user_id=user_id,
                version=version,
                cover_page_id=15217,  # No cover page for individual volumes
                trailing_page_id=None,  # No trailing page for individual volumes
                volume_number=volume_number,
                include_toc=True,
                toc_title="Table of Contents",
                toc_levels="1-2"
            )
            break

        # Move the generated DOCX to our dedicated folder structure
        if file_path and os.path.exists(file_path):
            # Create target path in our dedicated folder
            docx_filename = f"draft_volume_{volume_number}_{opportunity_id}.docx"
            target_path = os.path.join(folder_path, "docx", docx_filename)

            # Create the DOCX directory if it doesn't exist
            os.makedirs(os.path.dirname(target_path), exist_ok=True)

            # Move the file to our dedicated folder
            shutil.move(file_path, target_path)

            print(f"Exported draft for volume {volume_number} to DOCX using controller: {target_path}")
            return target_path
        else:
            print(f"Error: DOCX generation failed for volume {volume_number}")
            return ""

    except Exception as e:
        print(f"Error exporting draft to DOCX using controller: {e}")
        return ""


async def main():
    """
    Enhanced pipeline following proper flow: compliance → TOC → outline → draft for each volume.
    """

    # Configuration
    # opportunity_id = "iRiYNgd8RC"
    # opportunity_id = "GCP603BfMN"
    # opportunity_id = "vSe1unlCj9" #4344
    # opportunity_id = "fL2Ho9gHUj" #4380,
    # opportunity_id = "YypKJRYV2J"
    # opportunity_id = "GK7HhOFNg0"
    opportunity_id = "rwpWgMHaAC"
    # opportunity_id = "0001387d276a4023981c26c55e40b582"
    

    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    client = "adeptengineeringsolutions"
    source = "custom"

    print("="*80)
    print("ENHANCED PIPELINE 6: PROPER FLOW IMPLEMENTATION")
    print("="*80)
    print(f"Opportunity ID: {opportunity_id}")
    print(f"Tenant ID: {tenant_id}")
    print(f"Client: {client}")
    print("Flow: compliance → TOC → outline → draft (per volume)")
    print("="*80)

    # Create dedicated folder for all generated content
    folder_path = create_dedicated_folder(opportunity_id)

    # Initialize services
    outline_service = ProposalOutlineService()
    content_compliance_service = ContentComplianceService()
    structure_compliance_service = StructureComplianceService()
    custom_controller = CustomOpportunitiesController
    tenant_controller = TenantController

    # Get tenant metadata
    tenant_metadata = ""
    async for db in get_customer_db():
        tenant = await tenant_controller.get_by_tenant_id(db, tenant_id)
        tenant_metadata = f"{tenant}"
        break

    print("\n" + "="*60)
    print("STEP 1: GENERATE COMPLIANCE DATA")
    print("="*60)

    # Generate structure compliance first (defines volumes)
    print("Generating structure compliance...")
    structure_compliance_result = await structure_compliance_service.generate_structure_compliance(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source
    )
    structure_compliance = structure_compliance_result.get("content", "")

    structure_data = structure_compliance_result.get("structured_data", {})

    print(f"✓ Structure compliance: {'VALID' if structure_compliance_result.get('is_valid') else 'INVALID'}")

    # Generate content compliance
    print("Generating content compliance...")
    content_compliance_result = await content_compliance_service.generate_content_compliance(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source,
        is_rfp=True
    )
    content_compliance = content_compliance_result.get("content", "")
    structured_content_data = content_compliance_result.get("structured_data", {})

    print(f"✓ Content compliance: {'VALID' if content_compliance_result.get('is_valid') else 'INVALID'}")

    # Save compliance data to files
    print("Saving compliance data to files...")
    save_compliance_to_files(content_compliance, structure_compliance, folder_path, opportunity_id)

    print("\n" + "="*60)
    print("STEP 2: SPLIT COMPLIANCE BY VOLUME")
    print("="*60)

    # Extract volume definitions from structure compliance
    if structure_data and "structure" in structure_data:
        volume_definitions = structure_data["structure"]
        print(f"✓ Found {len(volume_definitions)} volumes from structured data")
    else:
        # Fallback to parsing text
        structure_compliance_data = ProposalUtilities.extract_json_from_brackets(structure_compliance)
        if not structure_compliance_data or "structure" not in structure_compliance_data:
            print("Error: Invalid structure compliance JSON, cannot determine volumes")
            return
        volume_definitions = structure_compliance_data["structure"]
        print(f"✓ Found {len(volume_definitions)} volumes from text parsing")

    # Split compliance data by volume
    volumes = {}

    # Extract structure data
    for volume in volume_definitions:
        volume_title = volume.get("volume_title", "")
        volumes[volume_title] = {
            "structure": volume,
            "content": None
        }

    # Match content compliance to volumes
    if structured_content_data and "content_compliance" in structured_content_data:
        print("✅ Using structured content compliance data...")
        for compliance in structured_content_data["content_compliance"]:
            volume_title = compliance.get("volume_title", "")
            # Match volume titles
            for vol_key in volumes.keys():
                if volume_title.lower() in vol_key.lower() or vol_key.lower() in volume_title.lower():
                    volumes[vol_key]["content"] = compliance
                    break

    else:
        print("⚠️ No structured content compliance, will use full content for each volume")
        # Use full content compliance for each volume
        for vol_key in volumes.keys():
            volumes[vol_key]["content"] = {"content": content_compliance}

    print(f"✓ Split compliance into {len(volumes)} volumes: {list(volumes.keys())}")

    print("\n" + "="*60)
    print("STEP 3: PROCESS EACH VOLUME SEQUENTIALLY")
    print("="*60)
    print("Flow per volume: compliance → TOC → outline → draft → PDF")
    print("="*60)

    all_results = {}
    total_sections = 0

    # Process each volume through the complete pipeline
    for volume_title, volume_data in volumes.items():
        print(f"\n{'='*60}")
        print(f"PROCESSING VOLUME: {volume_title}")
        print(f"{'='*60}")

        # Step 3.1: Generate TOC for this volume
        print(f"Step 3.1: Generating TOC for {volume_title}...")

        # Prepare volume-specific data
        volume_structure = json.dumps({"structure": [volume_data["structure"]]}, indent=2)
        volume_content = json.dumps({"content_compliance": [volume_data["content"]]}, indent=2) if volume_data["content"] else "{}"

        # Generate TOC for this volume
        toc_result = await outline_service.generate_table_of_contents(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            volume_information=volume_structure,
            content_compliance=volume_content,
            is_rfp=True
        )

        # Extract TOC JSON
        toc_data = None
        if toc_result and "content" in toc_result:
            content = toc_result["content"]
            try:
                # Look for JSON in the content
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    toc_data = json.loads(json_str)
            except:
                pass

        if toc_data and "table_of_contents" in toc_data:
            volume_toc = toc_data["table_of_contents"]
            print(f"✓ TOC generated successfully with {len(volume_toc)} sections")
        else:
            print("⚠ TOC generation failed or no valid JSON found")
            volume_toc = []

        # Save TOC to JSON file
        volume_number = list(volumes.keys()).index(volume_title) + 1
        toc_json_path = save_toc_to_json(toc_result, volume_toc, volume_number, folder_path, opportunity_id, volume_title)
        total_sections += len(volume_toc)

        # Step 3.2: Generate outline for this volume using TOC
        print(f"Step 3.2: Generating outline for {volume_title}...")

        # Extract table of contents list from toc_data
        if toc_data and "table_of_contents" in toc_data:
            toc_list = toc_data["table_of_contents"]
        else:
            print("⚠ No valid TOC data found, using empty list")
            toc_list = []

        # Generate outline for this volume
        outline_result = await outline_service.generate_outline_markdown(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            table_of_contents=toc_list,
            is_rfp=True
        )

        # Extract outline data
        outline_data = None
        if outline_result and "outlines" in outline_result:
            outline_data = outline_result["outlines"]
            print(f"✓ Outline generated successfully with {len(outline_data)} sections")
        else:
            print("⚠ Outline generation failed")

        # Save outline to JSON
        outline_json_path = save_outline_to_json(outline_result, volume_number, folder_path, opportunity_id)

        # Step 3.3: Generate draft for this volume using TOC
        print(f"Step 3.3: Generating draft for {volume_title}...")

        if not toc_data or "table_of_contents" not in toc_data:
            print("⚠ No valid TOC data found, cannot generate draft")
            draft_result = None
        else:
            # Prepare compliance data for this specific volume
            volume_content_compliance = []
            volume_structure_compliance = []

            # Extract content compliance for this volume
            if volume_data.get("content"):
                volume_content_compliance = [volume_data["content"]]
            else:
                print(f"⚠️ No content compliance found for volume: {volume_title}")

            # Extract structure compliance for this volume
            if volume_data.get("structure"):
                volume_structure_compliance = [volume_data["structure"]]
            else:
                print(f"⚠️ No structure compliance found for volume: {volume_title}")

            print(f"✓ Prepared compliance data - Content: {len(volume_content_compliance)} items, Structure: {len(volume_structure_compliance)} items")

            # Generate draft for this volume using the correct method signature with compliance data
            draft_result = await outline_service.generate_draft(
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                source=source,
                client_short_name=client,
                tenant_metadata=tenant_metadata,
                table_of_contents=toc_list,
                content_compliance=volume_content_compliance,
                structure_compliance=volume_structure_compliance
            )

        # Extract draft data
        draft_data = None
        if draft_result and "draft" in draft_result:
            draft_data = draft_result["draft"]
            print(f"✓ Draft generated successfully with {len(draft_data)} sections")
        
        # Log quality metrics if available
        if draft_result and "generation_summary" in draft_result:
            summary = draft_result["generation_summary"]
            if "average_quality_score" in summary:
                print(f"✓ Average quality score: {summary['average_quality_score']:.1f}/100")
            if "quality_scores" in summary:
                quality_scores = summary["quality_scores"]
                if quality_scores:
                    print(f"✓ Quality scores range: {min(quality_scores):.1f}-{max(quality_scores):.1f}")
        else:
            print("⚠ Draft generation failed")

        # Save draft to JSON
        if draft_result:
            draft_json_path = save_draft_to_json(draft_result, volume_number, folder_path, opportunity_id)
        else:
            draft_json_path = ""

        # Step 3.4: Export PDFs for this volume
        print(f"Step 3.4: Exporting {volume_title} to PDF...")

        # Export outline to PDF
        outline_pdf_path = ""
        if outline_result:
            print(f"  Exporting outline for {volume_title} to PDF...")
            outline_pdf_path = await export_outline_to_pdf_using_controller(
                outline_result, volume_number, folder_path, opportunity_id, tenant_id, volume_toc
            )

        # Export draft to PDF
        draft_pdf_path = ""
        if draft_result:
            print(f"  Exporting draft for {volume_title} to PDF...")
            draft_pdf_path = await export_draft_to_pdf_using_controller(
                draft_result, volume_number, folder_path, opportunity_id, tenant_id, volume_toc
            )

        # Step 3.5: Export DOCX files for this volume
        print(f"Step 3.5: Exporting {volume_title} to DOCX...")

        # Export outline to DOCX
        outline_docx_path = ""
        if outline_result:
            print(f"  Exporting outline for {volume_title} to DOCX...")
            outline_docx_path = await export_outline_to_docx_using_controller(
                outline_result, volume_number, folder_path, opportunity_id, tenant_id, volume_toc
            )

        # Export draft to DOCX
        draft_docx_path = ""
        if draft_result:
            print(f"  Exporting draft for {volume_title} to DOCX...")
            draft_docx_path = await export_draft_to_docx_using_controller(
                draft_result, volume_number, folder_path, opportunity_id, tenant_id, volume_toc
            )

        # Store results for this volume
        all_results[volume_title] = {
            "volume_title": volume_title,
            "volume_number": volume_number,
            "compliance_data": volume_data,
            "toc_result": toc_result,
            "toc_extracted": toc_data,
            "outline_result": outline_result,
            "outline_extracted": outline_data,
            "draft_result": draft_result,
            "draft_extracted": draft_data,
            "toc_json_path": toc_json_path,
            "outline_json_path": outline_json_path,
            "draft_json_path": draft_json_path,
            "outline_pdf_path": outline_pdf_path,
            "draft_pdf_path": draft_pdf_path,
            "outline_docx_path": outline_docx_path,
            "draft_docx_path": draft_docx_path
        }

        print(f"✅ {volume_title} COMPLETELY PROCESSED:")
        print(f"   - TOC sections: {len(volume_toc)}")
        print(f"   - Outline sections: {len(outline_data) if outline_data else 0}")
        print(f"   - Draft sections: {len(draft_data) if draft_data else 0}")
        print(f"   - TOC JSON: {toc_json_path}")
        print(f"   - Outline JSON: {outline_json_path}")
        print(f"   - Draft JSON: {draft_json_path}")
        print(f"   - Outline PDF: {outline_pdf_path}")
        print(f"   - Draft PDF: {draft_pdf_path}")
        print(f"   - Outline DOCX: {outline_docx_path}")
        print(f"   - Draft DOCX: {draft_docx_path}")
        print(f"{'='*60}")

    print(f"\n🎉 ALL {len(volumes)} VOLUMES PROCESSED COMPLETELY!")
    print(f"Total sections across all volumes: {total_sections}")

    print("\n" + "="*60)
    print("STEP 4: UPDATE DATABASE")
    print("="*60)

    # Prepare database update fields
    update_fields = {
        "content_compliance": content_compliance,
        "structure_compliance": structure_compliance,
        # Note: Ignoring the single 'draft' column as requested
    }

    # Save TOCs and outlines for each volume (up to 5 volumes supported by table structure)
    for volume_title, results in all_results.items():
        volume_number = results["volume_number"]
        if volume_number <= 5:
            # Save TOC
            toc_field = f"toc_text" if volume_number == 1 else f"toc_text_{volume_number}"
            if results.get("toc_extracted") and "table_of_contents" in results["toc_extracted"]:
                update_fields[toc_field] = json.dumps(results["toc_extracted"]["table_of_contents"])

            # Save outline
            outline_field = f"proposal_outline_{volume_number}"
            if results.get("outline_result"):
                update_fields[outline_field] = json.dumps(results["outline_result"])

    # Update the database
    async for db in get_customer_db():
        await custom_controller.update_by_opportunity_id(db, opportunity_id, update_fields)
        break

    print("✓ Database updated with compliance data, TOCs, and outlines")

    # Save summary files in dedicated folder
    summary_data = {
        "opportunity_id": opportunity_id,
        "tenant_id": tenant_id,
        "client": client,
        "generated_at": datetime.now().isoformat(),
        "volumes_count": len(volumes),
        "total_sections": total_sections,
        "content_compliance": content_compliance,
        "structure_compliance": structure_compliance,
        "volume_results": all_results,
        "folder_path": folder_path
    }

    summary_path = os.path.join(folder_path, "generation_summary.json")
    with open(summary_path, 'w') as f:
        json.dump(summary_data, f, indent=2)

    print("="*80)
    print("ENHANCED PIPELINE COMPLETED SUCCESSFULLY!")
    print("="*80)
    print("PROPER FLOW IMPLEMENTED:")
    print("1. ✅ Generated structure and content compliance")
    print("2. ✅ Split compliance by volume")
    print("3. ✅ For each volume: compliance → TOC → outline → draft → PDF")
    print("4. ✅ Updated database with all generated content")
    print("="*80)
    print(f"Generated {len(volumes)} SEPARATE volumes from {total_sections} sections")
    print(f"All content saved to: {folder_path}")
    print(f"Database updated with:")
    print(f"   - Content compliance")
    print(f"   - Structure compliance")
    print(f"   - {len(all_results)} volume-specific TOCs")
    print(f"   - {len(all_results)} volume-specific outlines")
    print(f"   - Draft column IGNORED as requested")
    print("="*80)
    
    


if __name__ == "__main__":
    asyncio.run(main())