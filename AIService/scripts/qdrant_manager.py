#!/usr/bin/env python3
"""
Qdrant Collection Manager
========================

This script provides flexible management of Qdrant collections with three modes:

1. DELETE_ALL: Delete all existing collections in Qdrant
2. DELETE_OPPORTUNITY: Delete collections for a specific opportunity ID
3. ADD_OPPORTUNITY: Add embeddings for a specific opportunity ID

Usage:
    # Delete all collections
    python scripts/recreate_qdrant_for_vSe1unlCj9.py delete-all [--dry-run]

    # Delete collections for specific opportunity (auto-detect tenant)
    python scripts/qdrant_manager.py delete-opportunity --opportunity-id vSe1unlCj9 [--dry-run]

    # Delete collections for specific opportunity (explicit tenant)
    python scripts/qdrant_manager.py delete-opportunity --opportunity-id vSe1unlCj9 --tenant-id 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 [--dry-run]

    # Add embeddings for specific opportunity (auto-detect tenant)
    python scripts/qdrant_manager.py add-opportunity --opportunity-id vSe1unlCj9 [--dry-run]

    # Add embeddings for specific opportunity (explicit tenant)
    python scripts/qdrant_manager.py add-opportunity --opportunity-id vSe1unlCj9 --tenant-id 8d9e9729-f7bd-44a0-9cf1-777f532a2db2 [--dry-run]

    # Add embeddings for ALL opportunities in the database
    python scripts/qdrant_manager.py add-all-opportunities [--dry-run]

    # Inspect all collections in Qdrant
    python scripts/qdrant_manager.py inspect-collections

    # Inspect a specific opportunity's collection
    python scripts/qdrant_manager.py inspect-opportunity --opportunity-id vSe1unlCj9 [--tenant-id 8d9e9729-f7bd-44a0-9cf1-777f532a2db2]

    # Query a specific opportunity's collection
    python scripts/qdrant_manager.py query-opportunity --opportunity-id vSe1unlCj9 --query "technical requirements" [--limit 10]
"""

import asyncio
import argparse
import sys
import os
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum

# Add the parent directory to the path so we can import from AIService
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger
from qdrant_client import QdrantClient

from config import settings
from database import get_customer_db, get_kontratar_db
from models.customer_models import CustomOpportunityTableInfo
from models.kontratar_models import ChromaDBInstanceMapping
from services.data_load.process_document import DocumentProcessingService
from services.chroma.chroma_service import ChromaService


class OperationMode(Enum):
    DELETE_ALL = "delete_all"
    DELETE_OPPORTUNITY = "delete_opportunity"
    ADD_OPPORTUNITY = "add_opportunity"
    ADD_ALL_OPPORTUNITIES = "add_all_opportunities"
    INSPECT_COLLECTIONS = "inspect_collections"
    INSPECT_OPPORTUNITY = "inspect_opportunity"
    QUERY_OPPORTUNITY = "query_opportunity"


class QdrantManager:
    def __init__(self, mode: OperationMode, opportunity_id: Optional[str] = None,
                 tenant_id: Optional[str] = None, dry_run: bool = False,
                 query_text: Optional[str] = None, query_limit: int = 5):
        self.mode = mode
        self.opportunity_id = opportunity_id
        self.tenant_id = tenant_id
        self.dry_run = dry_run
        self.query_text = query_text
        self.query_limit = query_limit

        # Initialize services
        self.qdrant_client = QdrantClient(url=settings.qdrant_url)
        self.chroma_service = ChromaService(
            embedding_api_url="http://ai.kontratar.com:5000"
        )

        # Statistics
        self.stats = {
            "collections_deleted": 0,
            "collections_created": 0,
            "documents_processed": 0,
            "chunks_created": 0,
            "opportunities_processed": 0,
            "opportunities_failed": 0,
            "start_time": None,
            "end_time": None,
            "error": None
        }

    async def get_tenant_id_for_opportunity(self, opportunity_id: str) -> Optional[str]:
        """Retrieve tenant_id for a given opportunity_id from the database"""
        logger.info(f"Looking up tenant_id for opportunity {opportunity_id}")

        try:
            async for db in get_customer_db():
                query = select(CustomOpportunityTableInfo.tenant_id).where(
                    CustomOpportunityTableInfo.opps_id == opportunity_id
                ).limit(1)

                result = await db.execute(query)
                tenant_id = result.scalar_one_or_none()

                if tenant_id:
                    logger.info(f"Found tenant_id: {tenant_id} for opportunity {opportunity_id}")
                    return tenant_id
                else:
                    logger.error(f"No tenant_id found for opportunity {opportunity_id}")
                    return None

        except Exception as e:
            logger.error(f"Error retrieving tenant_id for opportunity {opportunity_id}: {e}")
            return None

    async def get_all_opportunities(self) -> List[Dict[str, str]]:
        """Retrieve all opportunity_id and tenant_id pairs from the database"""
        logger.info("Retrieving all opportunities from database")

        opportunities = []
        try:
            async for db in get_customer_db():
                query = select(
                    CustomOpportunityTableInfo.opps_id,
                    CustomOpportunityTableInfo.tenant_id
                ).where(
                    CustomOpportunityTableInfo.opps_id.isnot(None),
                    CustomOpportunityTableInfo.tenant_id.isnot(None)
                ).distinct()

                result = await db.execute(query)
                rows = result.fetchall()

                for row in rows:
                    opportunities.append({
                        "opportunity_id": row.opps_id,
                        "tenant_id": row.tenant_id
                    })

                break  # Only process first database session

            logger.info(f"Found {len(opportunities)} opportunities in database")
            return opportunities

        except Exception as e:
            logger.error(f"Error retrieving opportunities from database: {e}")
            return []

    async def ensure_tenant_id(self):
        """Ensure we have a tenant_id, either provided or auto-detected"""
        if not self.opportunity_id:
            return True  # No opportunity_id means we don't need tenant_id (e.g., delete_all mode)

        if not self.tenant_id:
            logger.info("No tenant_id provided, attempting to auto-detect from database")
            self.tenant_id = await self.get_tenant_id_for_opportunity(self.opportunity_id)

        if not self.tenant_id:
            raise ValueError(f"Could not determine tenant_id for opportunity {self.opportunity_id}")

        return True

    async def delete_all_collections(self):
        """Delete all existing collections in Qdrant"""
        logger.info("Starting deletion of all Qdrant collections")

        try:
            # Get all collections
            collections = self.qdrant_client.get_collections()
            collection_names = [col.name for col in collections.collections]

            logger.info(f"Found {len(collection_names)} collections to delete")

            if self.dry_run:
                logger.info("DRY RUN: Would delete the following collections:")
                for name in collection_names:
                    logger.info(f"  - {name}")
                self.stats["collections_deleted"] = len(collection_names)
                return

            # Delete each collection
            for collection_name in collection_names:
                try:
                    self.qdrant_client.delete_collection(collection_name)
                    logger.info(f"Deleted collection: {collection_name}")
                    self.stats["collections_deleted"] += 1
                except Exception as e:
                    logger.error(f"Error deleting collection {collection_name}: {e}")

            logger.info(f"Successfully deleted {self.stats['collections_deleted']} collections")

        except Exception as e:
            logger.error(f"Error during collection deletion: {e}")
            raise

    async def delete_opportunity_collections(self):
        """Delete collections for a specific opportunity"""
        if not self.opportunity_id or not self.tenant_id:
            raise ValueError("opportunity_id and tenant_id are required for delete_opportunity mode")

        logger.info(f"Deleting collections for opportunity {self.opportunity_id}")

        try:
            # Get all collections
            collections = self.qdrant_client.get_collections()
            collection_names = [col.name for col in collections.collections]

            # Find collections related to this opportunity
            matching_collections = []

            # Check for direct name match (new naming convention)
            direct_name = f"{self.tenant_id}_{self.opportunity_id}"
            for name in collection_names:
                if (name == direct_name or
                    name.startswith(f"{direct_name}.") or
                    self.opportunity_id in name or
                    self.tenant_id in name):
                    matching_collections.append(name)

            # Also check database mappings for hash-based collections
            async for db in get_kontratar_db():
                query = select(ChromaDBInstanceMapping).where(
                    ChromaDBInstanceMapping.unique_id == self.opportunity_id,
                    ChromaDBInstanceMapping.tenant_id == self.tenant_id
                )
                result = await db.execute(query)
                mappings = result.scalars().all()

                for mapping in mappings:
                    # Look for versioned collections
                    for name in collection_names:
                        if name.startswith(f"{mapping.collection_name}."):
                            matching_collections.append(name)
                break

            # Remove duplicates
            matching_collections = list(set(matching_collections))

            logger.info(f"Found {len(matching_collections)} collections to delete for opportunity {self.opportunity_id}")

            if self.dry_run:
                logger.info("DRY RUN: Would delete the following collections:")
                for name in matching_collections:
                    logger.info(f"  - {name}")
                self.stats["collections_deleted"] = len(matching_collections)
                return

            # Delete matching collections
            for collection_name in matching_collections:
                try:
                    self.qdrant_client.delete_collection(collection_name)
                    logger.info(f"Deleted collection: {collection_name}")
                    self.stats["collections_deleted"] += 1
                except Exception as e:
                    logger.error(f"Error deleting collection {collection_name}: {e}")

            logger.info(f"Successfully deleted {self.stats['collections_deleted']} collections for opportunity {self.opportunity_id}")

        except Exception as e:
            logger.error(f"Error during opportunity collection deletion: {e}")
            raise

    async def get_opportunity_data(self) -> Optional[str]:
        """Retrieve raw text data for the opportunity"""
        if not self.opportunity_id or not self.tenant_id:
            raise ValueError("opportunity_id and tenant_id are required")

        logger.info(f"Retrieving data for opportunity {self.opportunity_id}")

        try:
            async for db in get_customer_db():
                query = select(CustomOpportunityTableInfo).where(
                    CustomOpportunityTableInfo.opps_id == self.opportunity_id,
                    CustomOpportunityTableInfo.tenant_id == self.tenant_id,
                    CustomOpportunityTableInfo.opps_raw_text.isnot(None)
                )

                result = await db.execute(query)
                record = result.scalar_one_or_none()

                if not record:
                    logger.error(f"No data found for opportunity {self.opportunity_id}")
                    return None

                # Handle bytes vs string
                raw_text = record.opps_raw_text
                if isinstance(raw_text, bytes):
                    raw_text = raw_text.decode('utf-8', errors='ignore')

                logger.info(f"Retrieved raw text data: {len(raw_text)} characters")
                return raw_text

        except Exception as e:
            logger.error(f"Error retrieving opportunity data: {e}")
            return None

    async def process_and_store_data(self, raw_text: str):
        """Process the raw text using DocumentProcessingService and store in Qdrant"""
        logger.info("Processing raw text using DocumentProcessingService")
        
        try:
            # Step 1: Process the corpus (clean the text)
            processed_text = DocumentProcessingService.process_corpus(raw_text)
            logger.info(f"Processed text: {len(processed_text)} characters")
            
            # Step 2: Create semantic chunks
            chunks = DocumentProcessingService.semantic_chunk(
                processed_text, 
                chunk_size=1000, 
                chunk_overlap=200
            )
            logger.info(f"Created {len(chunks)} semantic chunks")
            self.stats["chunks_created"] = len(chunks)
            
            if self.dry_run:
                logger.info("DRY RUN: Would create collection and add documents")
                logger.info(f"  Collection name: {self.tenant_id}_{self.opportunity_id}")
                logger.info(f"  Number of chunks: {len(chunks)}")
                self.stats["collections_created"] = 1
                self.stats["documents_processed"] = len(chunks)
                return
            
            # Step 3: Add documents to Qdrant using the existing service
            collection_name = f"{self.tenant_id}_{self.opportunity_id}"
            result = await DocumentProcessingService.add_documents(
                collection_name=collection_name,
                documents=chunks
            )
            
            if result:
                logger.info(f"Successfully stored {len(chunks)} chunks in collection {collection_name}")
                self.stats["collections_created"] = 1
                self.stats["documents_processed"] = len(chunks)
            else:
                logger.error("Failed to store documents in Qdrant")
                raise Exception("Document storage failed")
                
        except Exception as e:
            logger.error(f"Error processing and storing data: {e}")
            raise

    async def process_all_opportunities(self):
        """Process all opportunities in the database"""
        logger.info("Starting batch processing of all opportunities")

        # Get all opportunities from database
        opportunities = await self.get_all_opportunities()

        if not opportunities:
            logger.warning("No opportunities found in database")
            return

        logger.info(f"Processing {len(opportunities)} opportunities")

        successful_count = 0
        failed_count = 0

        for i, opp in enumerate(opportunities, 1):
            opportunity_id = opp["opportunity_id"]
            tenant_id = opp["tenant_id"]

            logger.info(f"[{i}/{len(opportunities)}] Processing opportunity: {opportunity_id} (tenant: {tenant_id})")

            try:
                # Temporarily set the current opportunity and tenant
                original_opportunity_id = self.opportunity_id
                original_tenant_id = self.tenant_id

                self.opportunity_id = opportunity_id
                self.tenant_id = tenant_id

                # Process this opportunity
                raw_text = await self.get_opportunity_data()
                if raw_text:
                    await self.process_and_store_data(raw_text)
                else:
                    raise Exception(f"No data found for opportunity {opportunity_id}")

                successful_count += 1
                logger.info(f"✅ Successfully processed {opportunity_id}")

                # Restore original values
                self.opportunity_id = original_opportunity_id
                self.tenant_id = original_tenant_id

            except Exception as e:
                failed_count += 1
                logger.error(f"❌ Failed to process {opportunity_id}: {e}")
                continue

        # Update statistics
        self.stats["opportunities_processed"] = successful_count
        self.stats["opportunities_failed"] = failed_count

        logger.info(f"Batch processing completed:")
        logger.info(f"  ✅ Successful: {successful_count}")
        logger.info(f"  ❌ Failed: {failed_count}")
        logger.info(f"  📊 Total: {len(opportunities)}")

    async def inspect_all_collections(self):
        """Inspect all collections in Qdrant with detailed information"""
        logger.info("Inspecting all collections in Qdrant")

        try:
            collections = self.qdrant_client.get_collections()

            if not collections.collections:
                logger.info("No collections found in Qdrant")
                return

            logger.info(f"Found {len(collections.collections)} collections:")
            logger.info("="*80)

            for i, collection in enumerate(collections.collections, 1):
                collection_name = collection.name
                logger.info(f"[{i}] Collection: {collection_name}")

                try:
                    # Get collection info
                    collection_info = self.qdrant_client.get_collection(collection_name)
                    vectors_count = collection_info.vectors_count if collection_info.vectors_count is not None else 0
                    status = collection_info.status.value if collection_info.status else "unknown"

                    # Get collection config
                    config = collection_info.config
                    vector_size = config.params.vectors.size if config and config.params and config.params.vectors else "unknown"
                    distance = config.params.vectors.distance.value if config and config.params and config.params.vectors and config.params.vectors.distance else "unknown"

                    logger.info(f"    📊 Vectors: {vectors_count}")
                    logger.info(f"    📏 Vector Size: {vector_size}")
                    logger.info(f"    📐 Distance Metric: {distance}")
                    logger.info(f"    🔄 Status: {status}")

                    # Try to get a sample point to see payload structure
                    try:
                        sample_points = self.qdrant_client.scroll(
                            collection_name=collection_name,
                            limit=1,
                            with_payload=True
                        )

                        if sample_points[0]:  # points
                            sample_point = sample_points[0][0]
                            payload_keys = list(sample_point.payload.keys()) if sample_point.payload else []
                            logger.info(f"    🏷️  Payload Keys: {payload_keys}")

                            # Check for opportunity/tenant info
                            if 'opps_id' in payload_keys:
                                logger.info(f"    🎯 Opportunity ID: {sample_point.payload.get('opps_id', 'N/A')}")
                            if 'tenant_id' in payload_keys:
                                logger.info(f"    🏢 Tenant ID: {sample_point.payload.get('tenant_id', 'N/A')}")
                        else:
                            logger.info(f"    🏷️  No sample data available")

                    except Exception as e:
                        logger.warning(f"    ⚠️  Could not retrieve sample data: {e}")

                except Exception as e:
                    logger.error(f"    ❌ Error inspecting collection {collection_name}: {e}")

                logger.info("-" * 60)

        except Exception as e:
            logger.error(f"Error inspecting collections: {e}")

    async def inspect_opportunity_collection(self):
        """Inspect a specific opportunity's collection in detail"""
        logger.info(f"Inspecting collection for opportunity: {self.opportunity_id}")

        # Find collections related to this opportunity
        matching_collections = await self.find_opportunity_collections()

        if not matching_collections:
            logger.warning(f"No collections found for opportunity {self.opportunity_id}")
            return

        logger.info(f"Found {len(matching_collections)} collections for opportunity {self.opportunity_id}:")
        logger.info("="*80)

        for collection_name in matching_collections:
            logger.info(f"📦 Collection: {collection_name}")

            try:
                # Get detailed collection info - handle potential status enum validation error
                try:
                    collection_info = self.qdrant_client.get_collection(collection_name)
                    vectors_count = collection_info.vectors_count
                except Exception as collection_error:
                    # Check if this is the specific enum validation error for status
                    if "Input should be 'green', 'yellow' or 'red'" in str(collection_error):
                        logger.warning(f"    ⚠️  Collection status validation error (likely 'grey' status): {collection_error}")
                        logger.info(f"    📊 Total Vectors: Unable to determine (status validation error)")
                        vectors_count = None
                    else:
                        # Re-raise if it's a different error
                        raise collection_error

                if vectors_count is not None:
                    logger.info(f"    📊 Total Vectors: {vectors_count}")

                # Get all points with payload
                all_points = []
                offset = None

                while True:
                    scroll_result = self.qdrant_client.scroll(
                        collection_name=collection_name,
                        limit=100,
                        offset=offset,
                        with_payload=True,
                        with_vectors=False  # Don't need vectors for inspection
                    )

                    points, next_offset = scroll_result
                    all_points.extend(points)

                    if next_offset is None:
                        break
                    offset = next_offset

                logger.info(f"    📄 Retrieved {len(all_points)} points for analysis")

                if all_points:
                    # Analyze chunks
                    chunk_indices = []
                    document_lengths = []
                    sources = set()

                    for point in all_points:
                        payload = point.payload

                        # Collect chunk indices
                        chunk_idx = payload.get('chunk_index', payload.get('document_index'))
                        if chunk_idx is not None:
                            chunk_indices.append(chunk_idx)

                        # Collect document lengths
                        doc = payload.get('document', payload.get('text', ''))
                        if doc:
                            document_lengths.append(len(doc))

                        # Collect sources
                        source = payload.get('source', 'unknown')
                        sources.add(source)

                    # Statistics
                    if chunk_indices:
                        logger.info(f"    🔢 Chunk Range: {min(chunk_indices)} - {max(chunk_indices)}")
                        logger.info(f"    📝 Total Chunks: {len(chunk_indices)}")

                    if document_lengths:
                        avg_length = sum(document_lengths) / len(document_lengths)
                        logger.info(f"    📏 Avg Chunk Length: {avg_length:.0f} characters")
                        logger.info(f"    📏 Min/Max Length: {min(document_lengths)}/{max(document_lengths)}")

                    logger.info(f"    📂 Sources: {list(sources)}")

                    # Show sample payload
                    sample_payload = all_points[0].payload
                    logger.info(f"    🏷️  Sample Payload Keys: {list(sample_payload.keys())}")

                    # Show opportunity/tenant info
                    if 'opps_id' in sample_payload:
                        logger.info(f"    🎯 Opportunity ID: {sample_payload.get('opps_id')}")
                    if 'tenant_id' in sample_payload:
                        logger.info(f"    🏢 Tenant ID: {sample_payload.get('tenant_id')}")

            except Exception as e:
                logger.error(f"    ❌ Error inspecting collection {collection_name}: {e}")

            logger.info("-" * 60)

    async def find_opportunity_collections(self) -> List[str]:
        """Find all collections related to an opportunity"""
        collections = self.qdrant_client.get_collections()
        collection_names = [col.name for col in collections.collections]

        matching_collections = []

        # Check for direct name match (new naming convention)
        direct_name = f"{self.tenant_id}_{self.opportunity_id}"
        for name in collection_names:
            if (name == direct_name or
                name.startswith(f"{direct_name}.") or
                self.opportunity_id in name):
                matching_collections.append(name)

        # Also check database mappings for hash-based collections
        try:
            async for db in get_kontratar_db():
                query = select(ChromaDBInstanceMapping).where(
                    ChromaDBInstanceMapping.unique_id == self.opportunity_id
                )
                if self.tenant_id:
                    query = query.where(ChromaDBInstanceMapping.tenant_id == self.tenant_id)

                result = await db.execute(query)
                mappings = result.scalars().all()

                for mapping in mappings:
                    # Look for versioned collections
                    for name in collection_names:
                        if name.startswith(f"{mapping.collection_name}."):
                            matching_collections.append(name)
                break
        except Exception as e:
            logger.warning(f"Could not check database mappings: {e}")

        # Remove duplicates
        return list(set(matching_collections))

    async def query_opportunity_collection(self, query_text: str, limit: int = 5):
        """Query a specific opportunity's collection"""
        logger.info(f"Querying opportunity {self.opportunity_id} with: '{query_text}'")

        # Find collections for this opportunity
        matching_collections = await self.find_opportunity_collections()

        if not matching_collections:
            logger.warning(f"No collections found for opportunity {self.opportunity_id}")
            return

        # Use the first matching collection (or could query all)
        collection_name = matching_collections[0]
        logger.info(f"Using collection: {collection_name}")

        try:
            # Generate query embedding
            from utils.embedding_model import KontratarEmbeddings
            embeddings = KontratarEmbeddings("http://ai.kontratar.com:5000")
            query_embedding = embeddings.embed_query(query_text)

            logger.info(f"Generated query embedding with {len(query_embedding)} dimensions")

            # Search in Qdrant
            search_results = self.qdrant_client.search(
                collection_name=collection_name,
                query_vector=query_embedding,
                limit=limit,
                with_payload=True,
                score_threshold=0.0  # Include all results
            )

            if not search_results:
                logger.warning("No results found for the query")
                return

            logger.info(f"Found {len(search_results)} results:")
            logger.info("="*80)

            for i, result in enumerate(search_results, 1):
                score = result.score
                payload = result.payload
                document = payload.get('document', payload.get('text', 'No document text'))
                chunk_idx = payload.get('chunk_index', payload.get('document_index', 'N/A'))

                logger.info(f"[{i}] Score: {score:.4f} | Chunk: {chunk_idx}")

                # Show document preview (first 200 chars)
                preview = document[:200] + "..." if len(document) > 200 else document
                logger.info(f"    📄 Content: {preview}")

                # Show other metadata
                for key, value in payload.items():
                    if key not in ['document', 'text'] and value is not None:
                        logger.info(f"    🏷️  {key}: {value}")

                logger.info("-" * 60)

        except Exception as e:
            logger.error(f"Error querying collection: {e}")

    async def run(self):
        """Main execution method"""
        self.stats["start_time"] = datetime.now()

        try:
            logger.info("="*60)
            logger.info(f"Qrant Url: {settings.qdrant_url}")
            logger.info(f"QDRANT MANAGER - {self.mode.value.upper()}")
            logger.info("="*60)
            logger.info(f"Mode: {self.mode.value}")
            if self.opportunity_id:
                logger.info(f"Opportunity ID: {self.opportunity_id}")
            if self.tenant_id:
                logger.info(f"Tenant ID: {self.tenant_id}")
            logger.info(f"Dry Run: {self.dry_run}")
            logger.info("="*60)

            # Ensure we have tenant_id if needed
            await self.ensure_tenant_id()

            # Log final tenant_id if it was auto-detected
            if self.opportunity_id and self.tenant_id:
                logger.info(f"Using Tenant ID: {self.tenant_id}")

            if self.mode == OperationMode.DELETE_ALL:
                await self.delete_all_collections()

            elif self.mode == OperationMode.DELETE_OPPORTUNITY:
                await self.delete_opportunity_collections()

            elif self.mode == OperationMode.ADD_OPPORTUNITY:
                # Get opportunity data
                raw_text = await self.get_opportunity_data()
                if not raw_text:
                    raise Exception("No raw text data found for opportunity")

                # Process and store data
                await self.process_and_store_data(raw_text)

            elif self.mode == OperationMode.ADD_ALL_OPPORTUNITIES:
                # Process all opportunities in the database
                await self.process_all_opportunities()

            elif self.mode == OperationMode.INSPECT_COLLECTIONS:
                # Inspect all collections in Qdrant
                await self.inspect_all_collections()

            elif self.mode == OperationMode.INSPECT_OPPORTUNITY:
                # Inspect specific opportunity collection
                await self.inspect_opportunity_collection()

            elif self.mode == OperationMode.QUERY_OPPORTUNITY:
                # Query specific opportunity collection
                query_text = getattr(self, 'query_text', 'default query')
                limit = getattr(self, 'query_limit', 5)
                await self.query_opportunity_collection(query_text, limit)

            self.stats["end_time"] = datetime.now()
            duration = self.stats["end_time"] - self.stats["start_time"]

            logger.info("="*60)
            logger.info("EXECUTION COMPLETED SUCCESSFULLY")
            logger.info("="*60)
            logger.info(f"Mode: {self.mode.value}")
            logger.info(f"Collections deleted: {self.stats['collections_deleted']}")
            logger.info(f"Collections created: {self.stats['collections_created']}")
            logger.info(f"Documents processed: {self.stats['documents_processed']}")
            logger.info(f"Chunks created: {self.stats['chunks_created']}")
            if self.mode == OperationMode.ADD_ALL_OPPORTUNITIES:
                logger.info(f"Opportunities processed: {self.stats['opportunities_processed']}")
                logger.info(f"Opportunities failed: {self.stats['opportunities_failed']}")
            logger.info(f"Duration: {duration}")
            logger.info("="*60)

        except Exception as e:
            self.stats["error"] = str(e)
            self.stats["end_time"] = datetime.now()
            logger.error(f"Execution failed: {e}")
            raise


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Qdrant Collection Manager",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Delete all collections
  python scripts/qdrant_manager.py delete-all --dry-run

  # Delete collections for specific opportunity
  python scripts/qdrant_manager.py delete-opportunity --opportunity-id vSe1unlCj9 --tenant-id 8d9e9729-f7bd-44a0-9cf1-777f532a2db2

  # Add embeddings for specific opportunity
  python scripts/qdrant_manager.py add-opportunity --opportunity-id vSe1unlCj9 --tenant-id 8d9e9729-f7bd-44a0-9cf1-777f532a2db2

  # Add embeddings for ALL opportunities in the database
  python scripts/qdrant_manager.py add-all-opportunities --dry-run

  # Inspect all collections in Qdrant
  python scripts/qdrant_manager.py inspect-collections

  # Inspect a specific opportunity's collection
  python scripts/qdrant_manager.py inspect-opportunity --opportunity-id vSe1unlCj9

  # Query a specific opportunity's collection
  python scripts/qdrant_manager.py query-opportunity --opportunity-id vSe1unlCj9 --query "technical requirements"
        """
    )

    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Delete all command
    delete_all_parser = subparsers.add_parser("delete-all", help="Delete all collections in Qdrant")
    delete_all_parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")

    # Delete opportunity command
    delete_opp_parser = subparsers.add_parser("delete-opportunity", help="Delete collections for a specific opportunity")
    delete_opp_parser.add_argument("--opportunity-id", required=True, help="Opportunity ID to delete")
    delete_opp_parser.add_argument("--tenant-id", help="Tenant ID (optional - will be auto-detected if not provided)")
    delete_opp_parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")

    # Add opportunity command
    add_opp_parser = subparsers.add_parser("add-opportunity", help="Add embeddings for a specific opportunity")
    add_opp_parser.add_argument("--opportunity-id", required=True, help="Opportunity ID to process")
    add_opp_parser.add_argument("--tenant-id", help="Tenant ID (optional - will be auto-detected if not provided)")
    add_opp_parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")

    # Add all opportunities command
    add_all_parser = subparsers.add_parser("add-all-opportunities", help="Add embeddings for all opportunities in the database")
    add_all_parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")

    # Inspect collections command
    inspect_parser = subparsers.add_parser("inspect-collections", help="Inspect all collections in Qdrant")

    # Inspect opportunity command
    inspect_opp_parser = subparsers.add_parser("inspect-opportunity", help="Inspect a specific opportunity's collection")
    inspect_opp_parser.add_argument("--opportunity-id", required=True, help="Opportunity ID to inspect")
    inspect_opp_parser.add_argument("--tenant-id", help="Tenant ID (optional - will be auto-detected if not provided)")

    # Query opportunity command
    query_parser = subparsers.add_parser("query-opportunity", help="Query a specific opportunity's collection")
    query_parser.add_argument("--opportunity-id", required=True, help="Opportunity ID to query")
    query_parser.add_argument("--tenant-id", help="Tenant ID (optional - will be auto-detected if not provided)")
    query_parser.add_argument("--query", required=True, help="Query text to search for")
    query_parser.add_argument("--limit", type=int, default=5, help="Number of results to return (default: 5)")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        sys.exit(1)

    try:
        # Determine operation mode
        if args.command == "delete-all":
            mode = OperationMode.DELETE_ALL
            opportunity_id = None
            tenant_id = None
        elif args.command == "delete-opportunity":
            mode = OperationMode.DELETE_OPPORTUNITY
            opportunity_id = args.opportunity_id
            tenant_id = getattr(args, 'tenant_id', None)
        elif args.command == "add-opportunity":
            mode = OperationMode.ADD_OPPORTUNITY
            opportunity_id = args.opportunity_id
            tenant_id = getattr(args, 'tenant_id', None)
        elif args.command == "add-all-opportunities":
            mode = OperationMode.ADD_ALL_OPPORTUNITIES
            opportunity_id = None
            tenant_id = None
        elif args.command == "inspect-collections":
            mode = OperationMode.INSPECT_COLLECTIONS
            opportunity_id = None
            tenant_id = None
        elif args.command == "inspect-opportunity":
            mode = OperationMode.INSPECT_OPPORTUNITY
            opportunity_id = args.opportunity_id
            tenant_id = getattr(args, 'tenant_id', None)
        elif args.command == "query-opportunity":
            mode = OperationMode.QUERY_OPPORTUNITY
            opportunity_id = args.opportunity_id
            tenant_id = getattr(args, 'tenant_id', None)
        else:
            logger.error(f"Unknown command: {args.command}")
            sys.exit(1)

        # Get query parameters if this is a query command
        query_text = getattr(args, 'query', None)
        query_limit = getattr(args, 'limit', 5)
        dry_run = getattr(args, 'dry_run', False)

        manager = QdrantManager(
            mode=mode,
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            dry_run=dry_run,
            query_text=query_text,
            query_limit=query_limit
        )
        asyncio.run(manager.run())

        if manager.stats["error"]:
            sys.exit(1)
        else:
            sys.exit(0)

    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Script failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
