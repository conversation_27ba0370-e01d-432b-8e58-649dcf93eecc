#!/usr/bin/env python3
"""
Enhanced Proposal Scoring Script with Detailed Analysis
"""

import argparse
import json
import os
import sys
from pathlib import Path
import asyncio
from datetime import datetime

# Add project paths and change to AIService directory for .env loading
# Since this script is in AIService/scripts/, parent.parent gives us the project root
project_root = Path(__file__).parent.parent.parent
aiservice_path = project_root / "AIService"
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(aiservice_path))

# Change to AIService directory so .env file is loaded correctly
original_cwd = os.getcwd()
os.chdir(str(aiservice_path))

from services.proposal.constructive_criticism import generate_proposal_criticism
from services.proposal.compliance_schemas import ContentCompliance

# Change back to original directory
os.chdir(original_cwd)
opportuinity_id="rwpWgMHaAC"
def load_proposal_volumes(proposal_dir: str):
    """Load proposal volumes from drafts folder"""
    proposal_path = Path(proposal_dir)
    drafts_dir = proposal_path / 'drafts'
    
    volumes = []
    for draft_file in sorted(drafts_dir.glob('draft_volume_*.json')):
        with open(draft_file, 'r', encoding='utf-8') as f:
            volume_data = json.load(f)
            # The function expects List[List[Dict[str, Any]]]
            # Each volume is a list of sections (Dict[str, Any])
            volumes.append(volume_data['draft'])  # Extract the draft array
    
    return volumes

def load_content_compliance(proposal_dir: str):
    """Load content compliance from compliances folder and convert to ContentCompliance objects"""
    proposal_path = Path(proposal_dir)
    compliances_dir = proposal_path / 'compliances'

    compliance_file = compliances_dir / f'content_compliance_{opportuinity_id}.txt'

    with open(compliance_file, 'r', encoding='utf-8') as f:
        content = f.read()
        # Remove markdown code blocks if present
        if content.strip().startswith('```'):
            lines = content.strip().split('\n')
            content = '\n'.join(lines[1:-1])

        compliance_data = json.loads(content)

        # Convert to ContentCompliance objects
        compliance_objects = []
        for item in compliance_data['content_compliance']:
            # Handle page_limit - convert "N/A" to None
            page_limit = item.get('page_limit')
            if page_limit == "N/A" or page_limit is None:
                page_limit = None
            elif isinstance(page_limit, str):
                try:
                    page_limit = int(page_limit)
                except ValueError:
                    page_limit = None

            compliance_obj = ContentCompliance(
                volume_title=item['volume_title'],
                content=item['content'],
                page_limit=page_limit,
                evaluation_criteria=item.get('evaluation_criteria'),
                mandatory_sections=item.get('mandatory_sections')
            )
            compliance_objects.append(compliance_obj)

        return compliance_objects

def analyze_quality_metrics(proposal_dir: str):
    """Analyze quality metrics from generated proposals"""
    proposal_path = Path(proposal_dir)
    drafts_dir = proposal_path / 'drafts'
    
    quality_metrics = {
        "total_sections": 0,
        "sections_with_quality_scores": 0,
        "average_quality_score": 0,
        "quality_score_distribution": {},
        "sections_by_quality": {
            "excellent": 0,  # 90-100
            "good": 0,       # 80-89
            "fair": 0,       # 70-79
            "poor": 0,       # 60-69
            "failing": 0     # <60
        }
    }
    
    all_quality_scores = []
    
    for draft_file in sorted(drafts_dir.glob('draft_volume_*.json')):
        with open(draft_file, 'r', encoding='utf-8') as f:
            volume_data = json.load(f)
            
            if 'generation_summary' in volume_data:
                summary = volume_data['generation_summary']
                if 'quality_scores' in summary:
                    scores = summary['quality_scores']
                    all_quality_scores.extend(scores)
                    quality_metrics["sections_with_quality_scores"] += len(scores)
                    
                    for score in scores:
                        if score >= 90:
                            quality_metrics["sections_by_quality"]["excellent"] += 1
                        elif score >= 80:
                            quality_metrics["sections_by_quality"]["good"] += 1
                        elif score >= 70:
                            quality_metrics["sections_by_quality"]["fair"] += 1
                        elif score >= 60:
                            quality_metrics["sections_by_quality"]["poor"] += 1
                        else:
                            quality_metrics["sections_by_quality"]["failing"] += 1
            
            # Count total sections
            if 'draft' in volume_data:
                quality_metrics["total_sections"] += len(volume_data['draft'])
    
    if all_quality_scores:
        quality_metrics["average_quality_score"] = sum(all_quality_scores) / len(all_quality_scores)
        quality_metrics["min_quality_score"] = min(all_quality_scores)
        quality_metrics["max_quality_score"] = max(all_quality_scores)
        
        # Distribution analysis
        for score in all_quality_scores:
            range_key = f"{(score // 10) * 10}-{(score // 10) * 10 + 9}"
            quality_metrics["quality_score_distribution"][range_key] = quality_metrics["quality_score_distribution"].get(range_key, 0) + 1
    
    return quality_metrics

async def score_proposal_enhanced(proposal_dir: str):
    """Enhanced scoring using constructive criticism service with detailed analysis"""
    
    print(f"Loading proposal data from: {proposal_dir}")
    
    # Load volumes and compliance
    volumes = load_proposal_volumes(proposal_dir)
    content_compliance = load_content_compliance(proposal_dir)
    
    print(f"Found {len(volumes)} volumes and {len(content_compliance)} compliance requirements")
    
    # Analyze quality metrics
    print("Analyzing quality metrics...")
    quality_metrics = analyze_quality_metrics(proposal_dir)
    
    # Call constructive criticism service
    print("Calling constructive criticism service...")
    
    result = await generate_proposal_criticism(
        opportunity_id=opportuinity_id,
        tenant_id="8d9e9729-f7bd-44a0-9cf1-777f532a2db2", 
        source="custom",
        proposal_volumes=volumes,
        content_compliance=content_compliance,
        save_to_db=False
    )
    
    if result is None:
        print("❌ Criticism service returned no results")
        return None
    
    print("✓ Criticism analysis completed")
    
    # Extract the key metrics we want
    scoring_results = {
        "timestamp": datetime.now().isoformat(),
        "average_score": result.get("average_score", 0),
        "overall_assessment": result.get("overall_assessment", "No assessment available"),
        "critical_gaps": result.get("critical_gaps", []),
        "recommended_actions": result.get("recommended_actions", []),
        "executive_summary": result.get("executive_summary", "No summary available"),
        "quality_metrics": quality_metrics,
        "improvement_potential": calculate_improvement_potential(result, quality_metrics)
    }
    
    return scoring_results

def calculate_improvement_potential(criticism_result: dict, quality_metrics: dict) -> dict:
    """Calculate potential for score improvement"""
    current_score = criticism_result.get("average_score", 0)
    quality_score = quality_metrics.get("average_quality_score", 0)
    
    # Calculate potential improvements
    potential_improvements = {
        "current_criticism_score": current_score,
        "current_quality_score": quality_score,
        "potential_improvement": 0,
        "target_score": 0,
        "improvement_strategies": []
    }
    
    # If we have both scores, calculate potential
    if current_score > 0 and quality_score > 0:
        # Weighted average with quality score having more weight
        combined_score = (current_score * 0.4) + (quality_score * 0.6)
        potential_improvements["combined_score"] = combined_score
        
        # Calculate potential improvement
        if combined_score < 85:
            potential_improvements["potential_improvement"] = 85 - combined_score
            potential_improvements["target_score"] = 85
            
            # Suggest strategies based on gaps
            if current_score < 80:
                potential_improvements["improvement_strategies"].append("Focus on compliance adherence and evaluation criteria")
            if quality_score < 80:
                potential_improvements["improvement_strategies"].append("Enhance technical depth and specificity")
            if len(criticism_result.get("critical_gaps", [])) > 3:
                potential_improvements["improvement_strategies"].append("Address critical gaps identified in analysis")
    
    return potential_improvements

def main():
    parser = argparse.ArgumentParser(description='Enhanced proposal scoring with detailed analysis')
    parser.add_argument('--proposal-dir', required=True, help='Path to proposal directory')
    parser.add_argument('--output', help='Output file for results (default: enhanced_scoring_results.json)')
    
    args = parser.parse_args()
    
    if not args.output:
        proposal_name = Path(args.proposal_dir).name
        args.output = f"enhanced_scoring_{proposal_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        # Run the async scoring function
        results = asyncio.run(score_proposal_enhanced(args.proposal_dir))
        
        if results is None:
            print("❌ Scoring failed")
            sys.exit(1)
        
        # Save results
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Print enhanced summary
        print(f"\n🎯 ENHANCED SCORING RESULTS")
        print(f"=" * 50)
        print(f"Average Score: {results['average_score']:.1f}/100")
        print(f"Quality Score: {results['quality_metrics']['average_quality_score']:.1f}/100")
        
        if 'combined_score' in results['improvement_potential']:
            print(f"Combined Score: {results['improvement_potential']['combined_score']:.1f}/100")
        
        print(f"\n📊 QUALITY DISTRIBUTION:")
        for quality, count in results['quality_metrics']['sections_by_quality'].items():
            print(f"  {quality.title()}: {count} sections")
        
        print(f"\n🔍 CRITICAL GAPS: {len(results['critical_gaps'])} identified")
        for i, gap in enumerate(results['critical_gaps'][:3], 1):
            print(f"  {i}. {gap}")
        
        print(f"\n💡 RECOMMENDED ACTIONS: {len(results['recommended_actions'])} provided")
        for i, action in enumerate(results['recommended_actions'][:3], 1):
            print(f"  {i}. {action}")
        
        if results['improvement_potential']['potential_improvement'] > 0:
            print(f"\n🚀 IMPROVEMENT POTENTIAL:")
            print(f"  Potential Improvement: +{results['improvement_potential']['potential_improvement']:.1f} points")
            print(f"  Target Score: {results['improvement_potential']['target_score']}/100")
            print(f"  Strategies:")
            for strategy in results['improvement_potential']['improvement_strategies']:
                print(f"    • {strategy}")
        
        print(f"\n📄 Full results saved to: {args.output}")
        print("✅ Enhanced scoring completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()


