#!/usr/bin/env python3
"""
Simple Proposal Scoring Script
"""

import argparse
import json
import os
import sys
from pathlib import Path
import asyncio

# Add project paths and change to AIService directory for .env loading
# Since this script is in AIService/scripts/, parent.parent gives us the project root
project_root = Path(__file__).parent.parent.parent
aiservice_path = project_root / "AIService"
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(aiservice_path))

# Change to AIService directory so .env file is loaded correctly
original_cwd = os.getcwd()
os.chdir(str(aiservice_path))

from services.proposal.constructive_criticism import generate_proposal_criticism
from services.proposal.compliance_schemas import ContentCompliance

# Change back to original directory
os.chdir(original_cwd)
proposal_id = "rwpWgMHaAC"

def load_proposal_volumes(proposal_dir: str):
    """Load proposal volumes from drafts folder"""
    proposal_path = Path(proposal_dir)
    drafts_dir = proposal_path / 'drafts'
    
    volumes = []
    for draft_file in sorted(drafts_dir.glob('draft_volume_*.json')):
        with open(draft_file, 'r', encoding='utf-8') as f:
            volume_data = json.load(f)
            # The function expects List[List[Dict[str, Any]]]
            # Each volume is a list of sections (Dict[str, Any])
            volumes.append(volume_data['draft'])  # Extract the draft array
    
    return volumes

def load_content_compliance(proposal_dir: str):
    """Load content compliance from compliances folder and convert to ContentCompliance objects"""
    proposal_path = Path(proposal_dir)
    compliances_dir = proposal_path / 'compliances'

    compliance_file = compliances_dir / f'content_compliance_{proposal_id}.txt'

    with open(compliance_file, 'r', encoding='utf-8') as f:
        content = f.read()
        # Remove markdown code blocks if present
        if content.strip().startswith('```'):
            lines = content.strip().split('\n')
            content = '\n'.join(lines[1:-1])

        compliance_data = json.loads(content)

        # Convert to ContentCompliance objects
        compliance_objects = []
        for item in compliance_data['content_compliance']:
            # Handle page_limit - convert "N/A" to None
            page_limit = item.get('page_limit')
            if page_limit == "N/A" or page_limit is None:
                page_limit = None
            elif isinstance(page_limit, str):
                try:
                    page_limit = int(page_limit)
                except ValueError:
                    page_limit = None

            compliance_obj = ContentCompliance(
                volume_title=item['volume_title'],
                content=item['content'],
                page_limit=page_limit,
                evaluation_criteria=item.get('evaluation_criteria'),
                mandatory_sections=item.get('mandatory_sections')
            )
            compliance_objects.append(compliance_obj)

        return compliance_objects

async def score_proposal_simple(proposal_dir: str):
    """Simple scoring using constructive criticism service"""
    
    print(f"Loading proposal data from: {proposal_dir}")
    
    # Load volumes and compliance
    volumes = load_proposal_volumes(proposal_dir)
    content_compliance = load_content_compliance(proposal_dir)
    
    print(f"Found {len(volumes)} volumes and {len(content_compliance)} compliance requirements")
    
    # Call constructive criticism service
    print("Calling constructive criticism service...")
    
    result = await generate_proposal_criticism(
        opportunity_id=proposal_id,
        tenant_id="8d9e9729-f7bd-44a0-9cf1-777f532a2db2", 
        source="custom",
        proposal_volumes=volumes,
        content_compliance=content_compliance,
        save_to_db=False
    )
    
    if result is None:
        print(" Criticism service returned no results")
        return None
    
    print("✓ Criticism analysis completed")
    
    # Extract all the enhanced metrics from our improved criticism system
    scoring_results = {
        "average_score": result.get("average_score", 0),
        "dimension_scores": result.get("dimension_scores", {}),
        "scoring_breakdown": result.get("scoring_breakdown", {}),
        "overall_assessment": result.get("overall_assessment", "No assessment available"),
        "critical_gaps": result.get("critical_gaps", []),
        "recommended_actions": result.get("recommended_actions", []),
        "executive_summary": result.get("executive_summary", "No summary available"),
        "volume_count": result.get("volume_count", 0)
    }
    
    return scoring_results

def main():
    parser = argparse.ArgumentParser(description='Simple proposal scoring using constructive criticism')
    parser.add_argument('--proposal-dir', required=True, help='Path to proposal directory')
    parser.add_argument('--output', help='Output file for results (default: simple_scoring_results.json)')
    
    args = parser.parse_args()
    
    if not args.output:
        proposal_name = Path(args.proposal_dir).name
        args.output = f"simple_scoring_{proposal_name}_openai_3.json"
    
    try:
        # Run the async scoring function
        results = asyncio.run(score_proposal_simple(args.proposal_dir))
        
        if results is None:
            print(" Scoring failed")
            sys.exit(1)
        
        # Save results
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Print enhanced summary
        print(f"\n SCORING RESULTS")
        print(f"Average Score: {results['average_score']}")
        print(f"Volume Count: {results['volume_count']}")
        
        # Print dimension scores
        if results.get('dimension_scores'):
            print(f"\nDIMENSION SCORES:")
            for dimension, score in results['dimension_scores'].items():
                print(f"  {dimension.replace('_', ' ').title()}: {score:.1f}/100")
        
        # Print scoring breakdown
        if results.get('scoring_breakdown'):
            breakdown = results['scoring_breakdown']
            print(f"\nSCORING BREAKDOWN:")
            print(f"  Excellent (90+): {breakdown.get('excellent_volumes', 0)} volumes")
            print(f"  Good (80-89): {breakdown.get('good_volumes', 0)} volumes")
            print(f"  Fair (70-79): {breakdown.get('fair_volumes', 0)} volumes")
            print(f"  Poor (60-69): {breakdown.get('poor_volumes', 0)} volumes")
            print(f"  Very Poor (<60): {breakdown.get('very_poor_volumes', 0)} volumes")
        
        print(f"\nOverall Assessment: {results['overall_assessment']}")
        print(f"Critical Gaps: {len(results['critical_gaps'])} identified")
        print(f"Recommended Actions: {len(results['recommended_actions'])} provided")
        print(f"\nExecutive Summary:")
        print(f"{results['executive_summary']}")
        
        print(f"\n Full results saved to: {args.output}")
        print(" Simple scoring completed!")
        
    except Exception as e:
        print(f" Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
