import os
from typing import Optional,List

from pydantic_settings import BaseSettings
from pydantic import Field

class Settings(BaseSettings):
    # Database Configuration - now using environment variables
    # Kontratar Database (kontratar_main and kontratar_global schemas)
    kontratar_db_host: str = Field("localhost", env="KONTRATAR_DB_HOST")
    kontratar_db_port: int = Field(5432, env="KONTRATAR_DB_PORT")
    kontratar_db_name: str = Field("postgres", env="KONTRATAR_DB_NAME")
    kontratar_db_user: str = Field("postgres", env="KONTRATAR_DB_USER")
    kontratar_db_password: str = Field("password", env="KONTRATAR_DB_PASSWORD")
    
    # Customer Database (opportunity schema)
    customer_db_host: str = Field("localhost", env="CUSTOMER_DB_HOST")
    customer_db_port: int = Field(5432, env="CUSTOMER_DB_PORT")
    customer_db_name: str = Field("postgres", env="CUSTOMER_DB_NAME")
    customer_db_user: str = Field("postgres", env="CUSTOMER_DB_USER")
    customer_db_password: str = Field("password", env="CUSTOMER_DB_PASSWORD")
    
    
    # Qdrant Configuration
    qdrant_host: str = Field("localhost", env="QDRANT_HOST")
    qdrant_port: int = Field(6333, env="QDRANT_PORT")
    qdrant_grpc_port: int = Field(6334, env="QDRANT_GRPC_PORT")
    qdrant_api_key: str = Field("", env="QDRANT_API_KEY")
    qdrant_timeout: int = Field(60, env="QDRANT_TIMEOUT")
    qdrant_prefer_grpc: bool = Field(True, env="QDRANT_PREFER_GRPC")
    
    # Application Configuration
    app_host: str = Field("0.0.0.0", env="APP_HOST")
    app_port: int = Field(8000, env="APP_PORT")
    debug: bool = Field(True, env="DEBUG")

    # LangSmith Configuration
    langchain_api_key: str = Field("", env="LANGCHAIN_API_KEY")
    langchain_project: str = Field("", env="LANGCHAIN_PROJECT")
    langchain_tracing_v2: str = Field("", env="LANGCHAIN_TRACING_V2")
    
    # Scheduler Configuration
    scheduler_interval_seconds: int = Field(60, env="SCHEDULER_INTERVAL_SECONDS")
    scheduler_enable_on_startup: bool = Field(False, env="SCHEDULER_ENABLE_ON_STARTUP")

    # Gemini API Configuration
    gemini_api_key: str = Field("", env="GEMINI_API_KEY")

    # OpenAI API Configuration
    openai_api_key: str = Field("", env="OPENAI_API_KEY")

    # LLM Configuration
    llm_provider: str = Field("ollama", env="LLM_PROVIDER")
    llm_model: str = Field("gemma3:27b", env="LLM_MODEL")
    
    # Webhooks
    discord_webhook_url: str = Field("", env="DISCORD_WEBHOOK_URL")

    # Embedding API Configuration
    embedding_api_host: str = Field("localhost", env="EMBEDDING_API_HOST")
    embedding_api_port: int = Field(5000, env="EMBEDDING_API_PORT")
    embedding_api_timeout: int = Field(120, env="EMBEDDING_API_TIMEOUT")
    embedding_provider: str = Field("openai", env="EMBEDDING_PROVIDER")  # openai, gemini, sentence-transformers, ollama
    embedding_model: str = Field("text-embedding-3-small", env="EMBEDDING_MODEL")


    # Qdrant Configuration Properties
    @property
    def qdrant_url(self) -> str:
        return f"http://{self.qdrant_host}:{self.qdrant_port}"
    
    @property
    def qdrant_grpc_url(self) -> str:
        return f"http://{self.qdrant_host}:{self.qdrant_grpc_port}"

    @property
    def embedding_api_url(self) -> str:
        return f"http://{self.embedding_api_host}:{self.embedding_api_port}"



    class Config:
        extra = "ignore"
        env_file = ".env"
        case_sensitive = False
        extra = "allow"


settings = Settings()
print("Loaded Qdrant URL:", settings.qdrant_url)
print("loaded langchain_api_key:", settings.langchain_api_key)


def get_kontratar_db_url() -> str:
    """Get the database URL for kontratar database"""
    return f"postgresql+asyncpg://{settings.kontratar_db_user}:{settings.kontratar_db_password}@{settings.kontratar_db_host}:{settings.kontratar_db_port}/{settings.kontratar_db_name}"


def get_customer_db_url() -> str:
    """Get the database URL for customer database"""
    return f"postgresql+asyncpg://{settings.customer_db_user}:{settings.customer_db_password}@{settings.customer_db_host}:{settings.customer_db_port}/{settings.customer_db_name}" 
