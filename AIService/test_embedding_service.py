#!/usr/bin/env python3
"""
Test Embedding Service
======================

This script tests the embedding service to ensure it's working correctly.
It can test both the local flexible embedding server and the original KontratarEmbeddings class.

Usage:
    python test_embedding_service.py [--url http://localhost:5000] [--test-batch]
"""

import argparse
import asyncio
import sys
import time
from typing import List
import requests
from loguru import logger

# Add the current directory to the path
sys.path.append('.')

def test_embedding_api_direct(url: str, texts: List[str]) -> bool:
    """Test the embedding API directly via HTTP"""
    try:
        logger.info(f"Testing embedding API at {url}")
        
        # Test health endpoint
        logger.info("Testing health endpoint...")
        health_response = requests.get(f"{url}/health", timeout=10)
        if health_response.status_code == 200:
            health_data = health_response.json()
            logger.info(f"✅ Health check passed: {health_data}")
        else:
            logger.error(f"❌ Health check failed: {health_response.status_code}")
            return False
        
        # Test batch embedding
        logger.info(f"Testing batch embedding with {len(texts)} texts...")
        start_time = time.time()
        
        response = requests.post(
            f"{url}/embed_batch",
            json={"texts": texts},
            timeout=60
        )
        
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            embeddings = data.get("embeddings", [])
            
            if len(embeddings) == len(texts):
                embedding_dim = len(embeddings[0]) if embeddings else 0
                logger.info(f"✅ Batch embedding successful:")
                logger.info(f"   - Texts processed: {len(embeddings)}")
                logger.info(f"   - Embedding dimension: {embedding_dim}")
                logger.info(f"   - Processing time: {end_time - start_time:.2f}s")
                logger.info(f"   - Speed: {len(texts)/(end_time - start_time):.1f} texts/second")
                return True
            else:
                logger.error(f"❌ Embedding count mismatch: expected {len(texts)}, got {len(embeddings)}")
                return False
        else:
            logger.error(f"❌ Batch embedding failed: {response.status_code} - {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        logger.error(f"❌ Cannot connect to embedding service at {url}")
        logger.info("Make sure the embedding server is running:")
        logger.info("  python start_embedding_server.py")
        return False
    except requests.exceptions.Timeout:
        logger.error(f"❌ Timeout connecting to embedding service at {url}")
        return False
    except Exception as e:
        logger.error(f"❌ Error testing embedding API: {e}")
        return False

def test_kontratar_embeddings(url: str, texts: List[str]) -> bool:
    """Test using the KontratarEmbeddings class"""
    try:
        logger.info(f"Testing KontratarEmbeddings class with URL: {url}")
        
        from utils.embedding_model import KontratarEmbeddings
        
        embeddings_client = KontratarEmbeddings(url)
        
        # Test single embedding
        logger.info("Testing single embedding...")
        start_time = time.time()
        single_embedding = embeddings_client.embed_query(texts[0])
        end_time = time.time()
        
        if single_embedding and len(single_embedding) > 0:
            logger.info(f"✅ Single embedding successful:")
            logger.info(f"   - Embedding dimension: {len(single_embedding)}")
            logger.info(f"   - Processing time: {end_time - start_time:.2f}s")
        else:
            logger.error("❌ Single embedding failed")
            return False
        
        # Test batch embedding
        logger.info(f"Testing batch embedding with {len(texts)} texts...")
        start_time = time.time()
        batch_embeddings = embeddings_client.embed_documents(texts)
        end_time = time.time()
        
        if batch_embeddings and len(batch_embeddings) == len(texts):
            embedding_dim = len(batch_embeddings[0]) if batch_embeddings else 0
            logger.info(f"✅ Batch embedding successful:")
            logger.info(f"   - Texts processed: {len(batch_embeddings)}")
            logger.info(f"   - Embedding dimension: {embedding_dim}")
            logger.info(f"   - Processing time: {end_time - start_time:.2f}s")
            logger.info(f"   - Speed: {len(texts)/(end_time - start_time):.1f} texts/second")
            return True
        else:
            logger.error(f"❌ Batch embedding failed: expected {len(texts)}, got {len(batch_embeddings) if batch_embeddings else 0}")
            return False
            
    except ImportError as e:
        logger.error(f"❌ Cannot import KontratarEmbeddings: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Error testing KontratarEmbeddings: {e}")
        return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Test Embedding Service",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Test local embedding server
  python test_embedding_service.py

  # Test with custom URL
  python test_embedding_service.py --url http://localhost:8080

  # Test with larger batch
  python test_embedding_service.py --test-batch

  # Test original ai.kontratar.com (will likely fail)
  python test_embedding_service.py --url http://ai.kontratar.com:5000
        """
    )
    
    parser.add_argument("--url", default="http://localhost:5000", 
                       help="Embedding service URL (default: http://localhost:5000)")
    parser.add_argument("--test-batch", action="store_true",
                       help="Test with larger batch of texts")
    
    args = parser.parse_args()
    
    # Prepare test texts
    if args.test_batch:
        test_texts = [
            "This is a test document for embedding generation.",
            "Government contracting requires detailed technical specifications.",
            "The proposal must address all mandatory requirements.",
            "Quality assurance processes ensure deliverable standards.",
            "Project management methodologies guide implementation.",
            "Risk mitigation strategies protect project success.",
            "Stakeholder engagement facilitates smooth execution.",
            "Performance metrics measure project effectiveness.",
            "Documentation standards ensure knowledge transfer.",
            "Training programs develop team capabilities."
        ]
    else:
        test_texts = [
            "This is a test document for embedding generation.",
            "Government contracting requires detailed technical specifications.",
            "The proposal must address all mandatory requirements."
        ]
    
    logger.info("="*80)
    logger.info("EMBEDDING SERVICE TEST")
    logger.info("="*80)
    logger.info(f"URL: {args.url}")
    logger.info(f"Test texts: {len(test_texts)}")
    logger.info("="*80)
    
    # Test 1: Direct API test
    logger.info("🧪 Test 1: Direct API Test")
    logger.info("-" * 40)
    api_success = test_embedding_api_direct(args.url, test_texts)
    
    print()
    
    # Test 2: KontratarEmbeddings class test
    logger.info("🧪 Test 2: KontratarEmbeddings Class Test")
    logger.info("-" * 40)
    class_success = test_kontratar_embeddings(args.url, test_texts)
    
    print()
    
    # Summary
    logger.info("="*80)
    logger.info("TEST SUMMARY")
    logger.info("="*80)
    logger.info(f"Direct API Test: {'✅ PASSED' if api_success else '❌ FAILED'}")
    logger.info(f"KontratarEmbeddings Test: {'✅ PASSED' if class_success else '❌ FAILED'}")
    
    if api_success and class_success:
        logger.info("🎉 All tests passed! Your embedding service is working correctly.")
        logger.info("You can now run your qdrant_manager.py script.")
    else:
        logger.error("💥 Some tests failed. Check the embedding service configuration.")
        if not api_success:
            logger.error("   - Make sure the embedding server is running")
            logger.error("   - Check the URL and port")
            logger.error("   - Verify API keys if using cloud providers")
        if not class_success:
            logger.error("   - Check KontratarEmbeddings class implementation")
            logger.error("   - Verify the API endpoint compatibility")
    
    logger.info("="*80)
    
    # Exit with appropriate code
    sys.exit(0 if (api_success and class_success) else 1)

if __name__ == "__main__":
    main()
