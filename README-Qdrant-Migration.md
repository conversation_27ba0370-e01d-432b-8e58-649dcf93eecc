# Qdrant Migration Guide

This document provides comprehensive instructions for migrating from ChromaDB to Qdrant in the GovBD proposal generation system.

## Overview

We have completely replaced ChromaDB with Qdrant as our vector database solution. This migration provides:

- **Better Performance**: Qdrant offers superior performance for similarity search operations
- **Improved Scalability**: Better handling of large-scale vector operations
- **Enhanced Reliability**: More robust for production workloads
- **Unified Interface**: The `ChromaService` now uses Qdrant under the hood while maintaining the same API

## What Changed

### ✅ Completed Changes

1. **Configuration Updates**
   - Added Qdrant configuration to `config.py`
   - Updated Docker Compose files
   - Removed ChromaDB dependencies

2. **Service Implementation**
   - `ChromaService` now uses Qdrant backend
   - Removed separate `QdrantService` class
   - Maintained existing interface for backward compatibility

3. **Dependencies**
   - Added `qdrant-client==1.8.2`
   - Removed `chromadb` and `langchain-chroma`

4. **Infrastructure**
   - Created `docker-compose.qdrant.yml` for local Qdrant setup
   - Single Qdrant instance (sufficient for development)

## Quick Start

### 1. Start Qdrant

```bash
# Start Qdrant using Docker Compose
docker-compose -f docker-compose.qdrant.yml up -d

# Verify Qdrant is running
curl http://localhost:6333/health
```

### 2. Install Dependencies

```bash
cd AIService
pip install -r requirements.txt
```

### 3. Setup Qdrant

```bash
# Setup and validate Qdrant
python scripts/setup_qdrant.py

# Test validation only
python scripts/setup_qdrant.py --validate-only
```

### 4. Environment Configuration

Add to your `.env` file:

```env
# Qdrant Configuration
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_GRPC_PORT=6334
QDRANT_API_KEY=
QDRANT_TIMEOUT=60
QDRANT_PREFER_GRPC=true
```

## Migration from ChromaDB

If you have existing ChromaDB data, use the migration script:

### 1. Test Connections

```bash
# Test both ChromaDB and Qdrant connections
python scripts/migrate_chromadb_to_qdrant.py --test-connections
```

### 2. Dry Run Migration

```bash
# See what would be migrated without making changes
python scripts/migrate_chromadb_to_qdrant.py --dry-run
```

### 3. Run Migration

```bash
# Migrate all data from ChromaDB to Qdrant
python scripts/migrate_chromadb_to_qdrant.py

# Use custom batch size for large datasets
python scripts/migrate_chromadb_to_qdrant.py --batch-size 500
```

### 4. Validate Migration

The migration script automatically validates data integrity by comparing document counts between source and destination.

## Configuration Details

### Qdrant Configuration

| Setting | Default | Description |
|---------|---------|-------------|
| `QDRANT_HOST` | localhost | Qdrant server hostname |
| `QDRANT_PORT` | 6333 | HTTP API port |
| `QDRANT_GRPC_PORT` | 6334 | gRPC port (faster) |
| `QDRANT_API_KEY` | "" | API key (empty for local) |
| `QDRANT_TIMEOUT` | 60 | Request timeout in seconds |
| `QDRANT_PREFER_GRPC` | true | Use gRPC when available |

### Vector Configuration

- **Dimension**: 768 (KontratarEmbeddings)
- **Distance Metric**: Cosine
- **Index**: Automatically optimized by Qdrant

## Application Usage

### No Code Changes Required

The migration is designed to be transparent to your application code. Existing code using `ChromaService` will continue to work:

```python
from services.chroma.chroma_service import ChromaService

# This now uses Qdrant under the hood
chroma_service = ChromaService(
    embedding_api_url="http://ai.kontratar.com:5000"
)

# All existing methods work the same
chunks = await chroma_service.get_relevant_chunks(
    db, collection_name, query, n_results=5
)
```

### Key Features Maintained

- ✅ Opportunity-based collections
- ✅ Tenant isolation
- ✅ Collection versioning
- ✅ Metadata support
- ✅ Hybrid search capabilities
- ✅ Document chunking and indexing

## Development Setup

### 1. Local Development

```bash
# Start Qdrant
docker-compose -f docker-compose.qdrant.yml up -d

# Start your application
cd AIService
python main.py
```

### 2. Testing

```bash
# Run setup validation
python scripts/setup_qdrant.py --validate-only

# Test embeddings
python scripts/test_embeddings.py  # If you have this script
```

### 3. Monitoring

- **Qdrant UI**: http://localhost:6333/dashboard
- **Health Check**: http://localhost:6333/health
- **Collections**: http://localhost:6333/collections

## Production Deployment

### Environment Variables

For production, update your deployment with:

```yaml
environment:
  - QDRANT_HOST=your-qdrant-host
  - QDRANT_PORT=6333
  - QDRANT_API_KEY=your-api-key
  # Remove old ChromaDB variables
```

### Scaling Considerations

- **Single Instance**: Sufficient for most use cases
- **Clustering**: Available in Qdrant Cloud for high availability
- **Backup**: Use Qdrant's snapshot functionality

## Troubleshooting

### Common Issues

1. **Connection Refused**
   ```bash
   # Check if Qdrant is running
   docker ps | grep qdrant
   curl http://localhost:6333/health
   ```

2. **Migration Failures**
   ```bash
   # Check ChromaDB accessibility
   python scripts/migrate_chromadb_to_qdrant.py --test-connections
   ```

3. **Performance Issues**
   ```bash
   # Check Qdrant memory usage
   docker stats qdrant-vector-db
   ```

### Logs

- **Application Logs**: Check for Qdrant connection errors
- **Qdrant Logs**: `docker logs qdrant-vector-db`
- **Migration Logs**: Detailed logging during migration process

## Rollback Plan

If you need to rollback to ChromaDB:

1. **Stop using new deployments**
2. **Restore ChromaDB instances**  
3. **Update configuration** to point back to ChromaDB
4. **Redeploy** with previous configuration

**Note**: The migration script preserves original ChromaDB data, so rollback is possible if needed.

## Performance Improvements

Expected improvements with Qdrant:

- **Query Speed**: 2-3x faster similarity searches
- **Memory Usage**: More efficient vector storage
- **Concurrent Operations**: Better handling of multiple requests
- **Scalability**: Improved performance under load

## Support

For issues related to:

- **Migration**: Check migration script logs and validate connections
- **Configuration**: Verify environment variables and Qdrant settings
- **Performance**: Monitor Qdrant dashboard and application metrics
- **Development**: Use validation scripts to ensure proper setup

## Next Steps

1. ✅ Complete migration setup
2. ✅ Validate all collections migrated
3. ✅ Test application functionality
4. ✅ Monitor performance improvements
5. ✅ Update deployment pipelines
6. ✅ Train team on new setup

---

**Migration Status**: ✅ Complete - Qdrant is now the primary vector database for the GovBD proposal generation system.

