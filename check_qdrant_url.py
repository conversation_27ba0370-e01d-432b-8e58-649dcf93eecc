#!/usr/bin/env python3
"""
Simple script to check the current Qdrant URL configuration.
"""

import sys
import os

# Add the AIService directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'AIService'))

try:
    from config import settings
    
    print("Current Qdrant Configuration:")
    print(f"  QDRANT_HOST: {settings.qdrant_host}")
    print(f"  QDRANT_PORT: {settings.qdrant_port}")
    print(f"  QDRANT_GRPC_PORT: {settings.qdrant_grpc_port}")
    print(f"  QDRANT_TIMEOUT: {settings.qdrant_timeout}")
    print(f"  QDRANT_PREFER_GRPC: {settings.qdrant_prefer_grpc}")
    print()
    print("Constructed URLs:")
    print(f"  Qdrant URL: {settings.qdrant_url}")
    print(f"  Qdrant gRPC URL: {settings.qdrant_grpc_url}")
    print()
    print("Embedding API Configuration:")
    print(f"  EMBEDDING_API_HOST: {settings.embedding_api_host}")
    print(f"  EMBEDDING_API_PORT: {settings.embedding_api_port}")
    print(f"  EMBEDDING_API_TIMEOUT: {settings.embedding_api_timeout}")
    print(f"  Embedding API URL: {settings.embedding_api_url}")
    
except Exception as e:
    print(f"Error loading configuration: {e}")
    sys.exit(1)
