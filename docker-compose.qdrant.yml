version: '3.8'

services:
  qdrant:
    image: qdrant/qdrant:v1.8.4
    container_name: qdrant-vector-db
    ports:
      - "6333:6333"  # HTTP API port
      - "6334:6334"  # gRPC port
    volumes:
      - qdrant_storage:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
      # Performance optimizations for development
      - QDRANT__SERVICE__MAX_REQUEST_SIZE_MB=64
      - QDRANT__SERVICE__ENABLE_CORS=true
    restart: unless-stopped
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - qdrant-network

volumes:
  qdrant_storage:
    driver: local

networks:
  qdrant-network:
    driver: bridge
