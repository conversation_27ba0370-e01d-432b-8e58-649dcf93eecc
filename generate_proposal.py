#!/usr/bin/env python3
"""
Simple Proposal Generator Script

This script generates a proposal using the exact same endpoint approach.
It creates a proposal queue item just like the API endpoint does.

Usage:
    python generate_proposal.py
"""

import asyncio
import json
import sys
import os

# Add the AIService directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'AIService'))

from services.queue_service.proposal_queue_service import ProposalQueueService
from database import get_customer_db


async def main():
    """Generate a proposal using the proposal queue endpoint approach"""

    # Configuration - Update these values as needed
    opportunity_id = "rwpWgMHaAC"  # Change this to your opportunity ID
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"  # Change this to your tenant ID
    client_short_name = "adeptengineeringsolutions"  # Change this to your client
    source = "custom"  # Options: "sam", "ebuy", "custom"

    print("🚀 PROPOSAL GENERATOR")
    print("=" * 50)
    print(f"Opportunity ID: {opportunity_id}")
    print(f"Tenant ID: {tenant_id}")
    print(f"Client: {client_short_name}")
    print(f"Source: {source}")
    print("=" * 50)

    # Create job instruction (same format as the API endpoint)
    job_instruction = {
        "opportunityId": opportunity_id,
        "clientShortName": client_short_name,
        "tenantId": tenant_id,
        "profileId": "2",
        "opportunityType": source,
        "sourceDocuments": [],
        "forceRefresh": False,
        "setForReview": True,
        "exportType": 1,
        "proposalRequestType": 1,
        "coverPage": None,
        "trailingPage": None,
        "systemPromptParameters": None,
        "isRFP": True,
        "aiPersonalityId": "6"
    }

    print("📋 Job Configuration:")
    print(json.dumps(job_instruction, indent=2))
    print("=" * 50)

    try:
        print("🔧 Creating proposal queue item...")

        # Use the database connection generator
        async for db in get_customer_db():
            new_item = await ProposalQueueService.create_proposal_queue_item(
                db=db,
                job_instruction=json.dumps(job_instruction),
                opps_id=opportunity_id,
                tenant_id=tenant_id,
                request_type=1,  # Standard proposal request type
                job_submitted_by="script_user"
            )

            if not new_item:
                print("❌ Failed to create proposal queue item")
                return

            print("✅ Proposal queue item created successfully!")
            print(f"Job ID: {new_item.job_id}")
            print(f"Queue ID: {new_item.id}")
            print()
            print("💡 The proposal will be processed by the scheduler service.")
            print("💡 Check the proposal_queue table for status updates.")
            break

    except Exception as e:
        print(f"❌ Error creating proposal queue item: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
